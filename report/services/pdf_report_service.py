"""
Service de génération de rapports PDF pour les détections
"""
import io
from datetime import datetime
from django.conf import settings
from django.http import HttpResponse
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY


class PDFReportService:
    """Service pour générer des rapports PDF de détections"""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()

    def _setup_custom_styles(self):
        """Configuration des styles personnalisés"""
        # Style pour le titre principal
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#1e40af'),
            fontName='Helvetica-Bold'
        ))

        # Style pour les sous-titres
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#374151'),
            fontName='Helvetica-Bold'
        ))

        # Style pour le texte normal
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=TA_JUSTIFY,
            fontName='Helvetica'
        ))

        # Style pour les métriques importantes
        self.styles.add(ParagraphStyle(
            name='MetricValue',
            parent=self.styles['Normal'],
            fontSize=18,
            fontName='Helvetica-Bold',
            textColor=colors.HexColor('#dc2626'),
            alignment=TA_CENTER
        ))

    def generate_detection_report(self, detection):
        """
        Génère un rapport PDF complet pour une détection
        """
        # Créer le buffer pour le PDF
        buffer = io.BytesIO()

        # Créer le document PDF
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # Construire le contenu
        story = []

        # En-tête du rapport
        self._add_header(story, detection)

        # Informations générales
        self._add_general_info(story, detection)

        # Analyse technique
        self._add_technical_analysis(story, detection)

        # Risques financiers
        self._add_financial_risks(story, detection)

        # Recommandations
        self._add_recommendations(story, detection)

        # Pied de page
        self._add_footer(story)

        # Générer le PDF
        doc.build(story)

        # Retourner le buffer
        buffer.seek(0)
        return buffer

    def _add_header(self, story, detection):
        """Ajoute l'en-tête du rapport"""
        # Logo et titre (simulé)
        title = Paragraph(
            "GOLD SENTINEL<br/>Rapport de Détection d'Orpaillage",
            self.styles['CustomTitle']
        )
        story.append(title)
        story.append(Spacer(1, 20))

        # Ligne de séparation
        story.append(HRFlowable(width="100%", thickness=2, color=colors.HexColor('#1e40af')))
        story.append(Spacer(1, 20))

        # Informations du rapport
        report_info = [
            ['Rapport N°:', f"GS-{detection.id:06d}"],
            ['Date de génération:', datetime.now().strftime('%d/%m/%Y à %H:%M')],
            ['Région:', detection.region.name if detection.region else 'Non spécifiée'],
            ['Statut:', detection.get_validation_status_display()],
        ]

        info_table = Table(report_info, colWidths=[4*cm, 6*cm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(info_table)
        story.append(Spacer(1, 30))

    def _add_general_info(self, story, detection):
        """Ajoute les informations générales de la détection"""
        story.append(Paragraph("1. INFORMATIONS GÉNÉRALES", self.styles['CustomHeading']))

        # Tableau des informations principales
        general_data = [
            ['Type de détection:', detection.get_detection_type_display()],
            ['Date de détection:', detection.detection_date.strftime('%d/%m/%Y') if detection.detection_date else 'Non spécifiée'],
            ['Coordonnées GPS:', f"{detection.latitude:.6f}, {detection.longitude:.6f}"],
            ['Surface estimée:', f"{detection.area_hectares:.2f} hectares"],
            ['Score de confiance:', f"{detection.confidence_score:.1%}"],
            ['Version algorithme:', detection.algorithm_version or 'Non spécifiée'],
        ]

        general_table = Table(general_data, colWidths=[5*cm, 8*cm])
        general_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8fafc')),
        ]))

        story.append(general_table)
        story.append(Spacer(1, 20))

    def _add_technical_analysis(self, story, detection):
        """Ajoute l'analyse technique détaillée"""
        story.append(Paragraph("2. ANALYSE TECHNIQUE", self.styles['CustomHeading']))

        # Scores d'anomalies
        if hasattr(detection, 'ndvi_anomaly_score') and detection.ndvi_anomaly_score is not None:
            anomaly_data = [
                ['Indicateur', 'Score', 'Interprétation'],
                ['NDVI (Végétation)', f"{detection.ndvi_anomaly_score:.1%}", self._interpret_score(detection.ndvi_anomaly_score)],
                ['NDWI (Eau)', f"{detection.ndwi_anomaly_score:.1%}", self._interpret_score(detection.ndwi_anomaly_score)],
                ['NDTI (Turbidité)', f"{detection.ndti_anomaly_score:.1%}", self._interpret_score(detection.ndti_anomaly_score)],
            ]

            anomaly_table = Table(anomaly_data, colWidths=[4*cm, 3*cm, 6*cm])
            anomaly_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1e40af')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(anomaly_table)
        else:
            story.append(Paragraph(
                "Les données d'analyse spectrale ne sont pas disponibles pour cette détection.",
                self.styles['CustomNormal']
            ))

        story.append(Spacer(1, 20))

    def _interpret_score(self, score):
        """Interprète un score d'anomalie"""
        if score >= 0.8:
            return "Anomalie très forte"
        elif score >= 0.6:
            return "Anomalie forte"
        elif score >= 0.4:
            return "Anomalie modérée"
        elif score >= 0.2:
            return "Anomalie faible"
        else:
            return "Pas d'anomalie"

    def _add_financial_risks(self, story, detection):
        """Ajoute l'analyse des risques financiers"""
        story.append(Paragraph("3. ÉVALUATION DES RISQUES FINANCIERS", self.styles['CustomHeading']))

        # Récupérer les données de risque financier
        try:
            from alert.models.financial_risk_model import FinancialRiskModel
            financial_risk = FinancialRiskModel.objects.filter(detection=detection).first()

            if financial_risk:
                # Métriques principales
                risk_data = [
                    ['Surface affectée:', f"{financial_risk.area_hectares:.2f} hectares"],
                    ['Coût par hectare:', f"{financial_risk.cost_per_hectare:,.0f} FCFA"],
                    ['Perte estimée:', f"{financial_risk.estimated_loss:,.0f} FCFA"],
                    ['Niveau de risque:', financial_risk.get_risk_level_display()],
                    ['Distance zone sensible:', f"{financial_risk.sensitive_zone_distance_km:.1f} km"],
                    ['Nombre d\'occurrences:', str(financial_risk.occurrence_count)],
                ]

                risk_table = Table(risk_data, colWidths=[5*cm, 8*cm])
                risk_table.setStyle(TableStyle([
                    ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 11),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                    ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8fafc')),
                ]))

                story.append(risk_table)
                story.append(Spacer(1, 15))

                # Mise en évidence de la perte estimée
                loss_highlight = Paragraph(
                    f"<b>IMPACT FINANCIER ESTIMÉ: {financial_risk.estimated_loss:,.0f} FCFA</b>",
                    self.styles['MetricValue']
                )
                story.append(loss_highlight)

                # Calcul du coût par mois si l'activité continue
                monthly_cost = financial_risk.estimated_loss / 12
                monthly_info = Paragraph(
                    f"Soit environ <b>{monthly_cost:,.0f} FCFA par mois</b> si l'activité d'orpaillage continue.",
                    self.styles['CustomNormal']
                )
                story.append(monthly_info)

            else:
                story.append(Paragraph(
                    "Aucune évaluation financière disponible pour cette détection.",
                    self.styles['CustomNormal']
                ))
        except ImportError:
            story.append(Paragraph(
                "Module d'évaluation financière non disponible.",
                self.styles['CustomNormal']
            ))

        story.append(Spacer(1, 20))

    def _add_recommendations(self, story, detection):
        """Ajoute les recommandations"""
        story.append(Paragraph("4. RECOMMANDATIONS", self.styles['CustomHeading']))

        # Recommandations basées sur le score de confiance et les risques
        recommendations = []

        if detection.confidence_score >= 0.8:
            recommendations.append("🔴 <b>Action immédiate requise</b> - Score de confiance très élevé")
            recommendations.append("• Déployer une équipe d'investigation sur le terrain dans les 24h")
            recommendations.append("• Alerter les autorités locales compétentes")
            recommendations.append("• Mettre en place une surveillance renforcée de la zone")
        elif detection.confidence_score >= 0.6:
            recommendations.append("🟡 <b>Investigation recommandée</b> - Score de confiance élevé")
            recommendations.append("• Planifier une mission de terrain dans les 72h")
            recommendations.append("• Analyser les images satellites récentes de la zone")
        else:
            recommendations.append("🟢 <b>Surveillance continue</b> - Score de confiance modéré")
            recommendations.append("• Maintenir la surveillance satellite de la zone")
            recommendations.append("• Programmer une vérification dans 2 semaines")

        # Recommandations spécifiques selon la surface
        if detection.area_hectares > 5:
            recommendations.append("• Zone d'impact importante - Coordonner avec plusieurs équipes")
        elif detection.area_hectares > 2:
            recommendations.append("• Zone d'impact modérée - Équipe standard recommandée")

        # Ajouter les recommandations au document
        for rec in recommendations:
            story.append(Paragraph(rec, self.styles['CustomNormal']))
            story.append(Spacer(1, 6))

        story.append(Spacer(1, 20))

    def _add_footer(self, story):
        """Ajoute le pied de page"""
        story.append(Spacer(1, 30))
        story.append(HRFlowable(width="100%", thickness=1, color=colors.grey))
        story.append(Spacer(1, 10))

        footer_text = f"""
        <i>Ce rapport a été généré automatiquement par le système Gold Sentinel le {datetime.now().strftime('%d/%m/%Y à %H:%M')}.<br/>
        Pour toute question concernant ce rapport, veuillez contacter l'équipe technique.<br/>
        © 2025 Gold Sentinel - Système de surveillance de l'orpaillage clandestin</i>
        """

        footer = Paragraph(footer_text, self.styles['CustomNormal'])
        story.append(footer)
