# 🌍 Guide d'Analyse Satellite

## 📋 **Vue d'ensemble**

Le module d'analyse satellite permet de :
- **Visualiser une carte satellite réelle** avec OpenStreetMap et imagerie Esri
- **Sélectionner une zone d'intérêt** sur la carte
- **Lancer une analyse spectrale** de la zone sélectionnée
- **Voir les détections** retournées par l'analyse

## 🗺️ **Fonctionnalités de la Carte**

### **Carte Interactive**
- Carte satellite Esri World Imagery centrée sur la Côte d'Ivoire
- Couches de base : Satellite et OpenStreetMap
- Zoom et navigation libres
- Affichage des détections existantes avec marqueurs colorés

### **Sélection de Zone**
- Outil de dessin de rectangle pour sélectionner une zone
- Coordonnées affichées en temps réel
- Validation des limites géographiques

### **Couches Spectrales**
- **NDVI** : Indice de végétation (vert)
- **NDWI** : Indice d'eau (bleu)
- **NDTI** : Indice de sol nu (orange)
- **Détections** : Marqueurs des sites détectés

## 🚀 **Lancement d'Analyse**

### **Prérequis**
- Permissions `CanLaunchAnalysis`
- Zone sélectionnée sur la carte
- Connexion au backend GEE

### **Processus**
1. **Sélectionner une zone** avec l'outil rectangle
2. **Cliquer "Lancer Analyse"**
3. **Attendre le traitement** (images satellites + IA)
4. **Voir les résultats** : nouvelles détections, alertes, investigations

### **Résultats**
- **Images traitées** : Nombre d'images Sentinel-2 analysées
- **Détections trouvées** : Nouveaux sites d'orpaillage détectés
- **Alertes générées** : Alertes automatiques créées
- **Investigations créées** : Dossiers d'enquête ouverts

## 🔧 **Configuration**

### **Variables d'environnement**
```bash
# Frontend (.env)
VITE_API_URL=http://localhost:8000/api
VITE_APP_NAME=GoldSentinel
VITE_APP_VERSION=1.0.0
```

### **Cartes et Imagerie**
- **OpenStreetMap** : Pas de clé API requise
- **Esri World Imagery** : Service gratuit pour l'imagerie satellite
- **Leaflet** : Bibliothèque de cartes open source

## 📡 **API Backend**

### **Endpoints utilisés**
- `POST /api/analysis/run/` - Lancer une analyse
- `GET /api/detections/` - Récupérer les détections
- `GET /api/spectral/maps/{id}/` - Cartes spectrales
- `GET /api/images/` - Images disponibles

### **Format de requête d'analyse**
```json
{
  "months_back": 3,
  "region_bounds": {
    "north": 8.5,
    "south": 7.5,
    "east": -2.5,
    "west": -3.5
  }
}
```

## 🎯 **Permissions**

### **Rôles autorisés**
- **Agent Analyste** : Peut lancer des analyses
- **Responsable Régional** : Peut lancer des analyses
- **Administrateur** : Accès complet

### **Restrictions**
- **Agent Terrain** : Visualisation uniquement
- **Agent Technique** : Visualisation uniquement

## 🔍 **Détections**

### **Types de détections**
- `MINING_SITE` : Site minier ⛏️
- `WATER_POLLUTION` : Pollution eau 💧
- `DEFORESTATION` : Déforestation 🌳
- `SOIL_DISTURBANCE` : Perturbation sol 🏗️

### **Niveaux de confiance**
- **Haute** (≥80%) : Marqueur rouge
- **Moyenne** (50-80%) : Marqueur orange
- **Faible** (<50%) : Marqueur jaune

### **Statuts de validation**
- `DETECTED` : Détecté automatiquement
- `VALIDATED` : Validé par un analyste
- `CONFIRMED` : Confirmé sur terrain
- `FALSE_POSITIVE` : Faux positif

## 📊 **Indices Spectraux**

### **NDVI (Normalized Difference Vegetation Index)**
- **Formule** : (NIR - Red) / (NIR + Red)
- **Usage** : Détection de végétation saine
- **Valeurs** : -1 à +1 (plus élevé = plus de végétation)

### **NDWI (Normalized Difference Water Index)**
- **Formule** : (Green - NIR) / (Green + NIR)
- **Usage** : Détection de surfaces en eau
- **Valeurs** : -1 à +1 (plus élevé = plus d'eau)

### **NDTI (Normalized Difference Tillage Index)**
- **Formule** : (SWIR1 - SWIR2) / (SWIR1 + SWIR2)
- **Usage** : Détection de sol nu/perturbé
- **Valeurs** : -1 à +1 (plus élevé = sol plus perturbé)

## 🛠️ **Développement**

### **Structure des composants**
```
src/
├── components/
│   ├── gee/
│   │   ├── GEEMap.tsx          # Carte principale
│   │   └── index.ts
│   └── detections/
│       ├── DetectionCard.tsx    # Carte de détection
│       ├── DetectionFilters.tsx # Filtres avancés
│       └── DetectionMap.tsx     # Carte des détections
├── pages/
│   ├── GEEAnalysisPage.tsx     # Page principale GEE
│   └── DetectionsListPage.tsx  # Liste des détections
├── services/
│   ├── gee.service.ts          # Service GEE
│   └── detection.service.ts    # Service détections
└── types/
    └── detection.types.ts      # Types TypeScript
```

### **Hooks utilisés**
- `useQuery` : Récupération des données
- `useMutation` : Lancement d'analyses
- `usePermissions` : Vérification des droits

## 🚨 **Gestion d'erreurs**

### **Erreurs communes**
- **Carte ne se charge pas** : Vérifier la connexion internet
- **Permissions insuffisantes** : Contacter admin
- **Zone trop grande** : Réduire la sélection
- **Pas d'images disponibles** : Choisir une autre période
- **Outils de dessin non disponibles** : Vérifier les permissions utilisateur

### **Messages d'erreur**
- Toast notifications pour les erreurs utilisateur
- Logs console pour le debug développeur
- Fallback UI pour les erreurs de chargement

## 📈 **Performance**

### **Optimisations**
- Lazy loading des composants carte
- Debounce sur les interactions utilisateur
- Cache des requêtes avec React Query
- Pagination des résultats

### **Limites**
- Maximum 1000 détections affichées simultanément
- Timeout de 5 minutes pour les analyses
- Rafraîchissement automatique toutes les 30 secondes

## 🔮 **Fonctionnalités futures**

- [ ] **Analyse en temps réel** avec WebSockets
- [ ] **Couches personnalisées** (température, humidité)
- [ ] **Historique des analyses** avec timeline
- [ ] **Export des données** en formats multiples
- [ ] **Notifications push** pour nouvelles détections
- [ ] **Mode hors ligne** avec cache local
