# 🗺️ Dépannage Carte Interactive Bondoukou

## 🔍 **Problème : Carte ne s'affiche pas sur le Dashboard**

### **Vérifications à effectuer :**

#### **1. Console du navigateur**
Ouvrez les outils de développement (F12) et vérifiez :
- Onglet **Console** : Recherchez les erreurs JavaScript
- Onglet **Network** : Vérifiez si les tuiles OpenStreetMap se chargent
- Messages de debug commençant par "BondoukouMap:"

#### **2. Erreurs communes**

**Erreur : "Cannot read properties of null"**
```
Solution : Problème d'initialisation du composant
- Vérifier que le conteneur de la carte a une hauteur définie
- S'assurer que Leaflet CSS est chargé
```

**Erreur : "L is not defined"**
```
Solution : Problème d'import Leaflet
- Vérifier que 'leaflet' est installé : npm list leaflet
- Redémarrer le serveur de développement
```

**Tuiles ne se chargent pas**
```
Solution : Problème de réseau ou CORS
- Vérifier la connexion internet
- Tester l'URL des tuiles : https://tile.openstreetmap.org/10/512/512.png
```

#### **3. Vérifications CSS**

**Carte invisible ou mal dimensionnée**
```css
/* Vérifier que ces styles sont appliqués */
.leaflet-container {
  height: 100% !important;
  width: 100% !important;
}
```

**Icônes manquantes**
```
Solution : Problème d'icônes Leaflet
- Vérifier que leaflet-custom.css est chargé
- Les icônes sont chargées depuis CDN
```

#### **4. Données de détections**

**Carte vide (pas de marqueurs)**
```
Vérifications :
1. Données de détections chargées : Voir console "Adding X detections to map"
2. Coordonnées valides : latitude/longitude non nulles
3. Permissions utilisateur : canViewStats() = true
```

### **🛠️ Solutions par étapes**

#### **Étape 1 : Vérifier l'installation**
```bash
cd frontend
npm list leaflet
npm list leaflet-draw
```

#### **Étape 2 : Redémarrer le serveur**
```bash
npm run dev
```

#### **Étape 3 : Vérifier les imports**
Fichiers à vérifier :
- `src/main.tsx` : Import de `leaflet/dist/leaflet.css`
- `src/components/maps/BondoukouMap.tsx` : Import de Leaflet

#### **Étape 4 : Tester avec données factices**
Modifier temporairement le composant pour tester :
```typescript
// Dans BondoukouMap.tsx, ajouter des données de test
const testDetections = [
  {
    id: 1,
    latitude: 8.0402,
    longitude: -2.8000,
    detection_type: 'MINING_SITE',
    validation_status: 'VALIDATED',
    confidence_score: 0.85,
    area_hectares: 2.5,
    detection_date: new Date().toISOString()
  }
];
```

### **🔧 Commandes de debug**

#### **Console navigateur**
```javascript
// Vérifier si Leaflet est chargé
console.log(window.L);

// Vérifier les éléments DOM
document.querySelector('.leaflet-container');

// Vérifier les styles CSS
getComputedStyle(document.querySelector('.leaflet-container'));
```

#### **Logs de debug activés**
Recherchez dans la console :
```
BondoukouMap: Initializing map with center: [8.0402, -2.8000] zoom: 10
BondoukouMap: Map initialized successfully
BondoukouMap: Adding X detections to map
```

### **📞 Support**

Si le problème persiste :
1. **Capturer les erreurs console** (screenshot)
2. **Vérifier la version de Leaflet** : `npm list leaflet`
3. **Tester sur un autre navigateur**
4. **Vérifier les permissions réseau** (proxy/firewall)

### **✅ Checklist de validation**

- [ ] Leaflet CSS chargé dans main.tsx
- [ ] Pas d'erreurs JavaScript dans la console
- [ ] Tuiles OpenStreetMap se chargent (onglet Network)
- [ ] Conteneur de carte a une hauteur définie
- [ ] Données de détections disponibles
- [ ] Permissions utilisateur correctes
- [ ] Messages de debug visibles en mode développement

---

**🎯 Objectif** : Carte interactive OpenStreetMap fonctionnelle avec marqueurs de détections dans le dashboard.
