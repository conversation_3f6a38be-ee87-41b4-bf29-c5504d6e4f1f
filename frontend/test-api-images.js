// Script de test pour vérifier l'API des images
// Exécuter avec: node test-api-images.js

const axios = require('axios');

const API_URL = 'http://localhost:8000/api/v1';

async function testImagesAPI() {
  console.log('🔍 Test de l\'API Images...');
  console.log('🔍 URL de base:', API_URL);

  try {
    // Test 1: Vérifier si le serveur répond
    console.log('\n1. Test de connexion au serveur...');
    const healthCheck = await axios.get(`${API_URL.replace('/api/v1', '')}/`);
    console.log('✅ Serveur accessible');

    // Test 2: Tester l'endpoint images sans authentification
    console.log('\n2. Test endpoint images sans auth...');
    try {
      const response = await axios.get(`${API_URL}/images/`);
      console.log('✅ Endpoint accessible sans auth');
      console.log('📊 Données:', response.data);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('🔒 Authentification requise (normal)');
      } else {
        console.log('❌ Erreur:', error.response?.status, error.response?.data);
      }
    }

    // Test 3: Tester avec un token factice
    console.log('\n3. Test avec token factice...');
    try {
      const response = await axios.get(`${API_URL}/images/`, {
        headers: {
          'Authorization': 'Bearer fake-token'
        }
      });
      console.log('✅ Réponse reçue');
      console.log('📊 Données:', response.data);
    } catch (error) {
      console.log('❌ Erreur avec token factice:', error.response?.status, error.response?.data);
    }

    // Test 4: Vérifier les endpoints disponibles
    console.log('\n4. Test des endpoints disponibles...');
    const endpoints = [
      '/images/',
      '/images/recent/',
      '/detections/',
      '/alerts/',
      '/auth/token/'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${API_URL}${endpoint}`);
        console.log(`✅ ${endpoint}: OK`);
      } catch (error) {
        const status = error.response?.status || 'NO_RESPONSE';
        console.log(`❌ ${endpoint}: ${status}`);
      }
    }

  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Le serveur Django n\'est probablement pas démarré sur le port 8000');
    }
  }
}

// Test avec authentification réelle (si token disponible)
async function testWithRealAuth() {
  console.log('\n🔐 Test avec authentification réelle...');
  
  // Simuler une connexion
  try {
    const loginResponse = await axios.post(`${API_URL}/auth/token/`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.access;
    console.log('✅ Connexion réussie');
    
    // Tester l'endpoint images avec le vrai token
    const imagesResponse = await axios.get(`${API_URL}/images/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Images récupérées avec succès');
    console.log('📊 Nombre d\'images:', imagesResponse.data.count || imagesResponse.data.length);
    console.log('📊 Structure:', Object.keys(imagesResponse.data));
    
    if (imagesResponse.data.results) {
      console.log('📊 Première image:', imagesResponse.data.results[0]);
    }
    
  } catch (error) {
    console.log('❌ Erreur d\'authentification:', error.response?.status, error.response?.data);
  }
}

// Exécuter les tests
async function runAllTests() {
  await testImagesAPI();
  await testWithRealAuth();
}

runAllTests();
