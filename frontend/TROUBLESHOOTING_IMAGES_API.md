# 🖼️ Dépannage API Images

## 🔍 **Problème : "Aucune image disponible dans la BD"**

### **Symptômes observés :**
- Console : `isLoading: true` en permanence
- Console : `imagesResponse: undefined`
- Interface : "Aucune image disponible dans la base de données"
- Token d'authentification présent

### **🔧 Étapes de diagnostic :**

#### **1. Vérifier l'URL de l'API**
```javascript
// Dans la console navigateur
console.log('VITE_API_URL:', import.meta.env.VITE_API_URL);
// Doit afficher: http://localhost:8000/api/v1
```

#### **2. Tester l'endpoint directement**
```bash
# Test avec curl
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/v1/images/

# Ou avec le bouton "Test API" dans l'interface (mode dev)
```

#### **3. Vérifier le serveur Django**
```bash
# Le serveur doit tourner sur le port 8000
python manage.py runserver 0.0.0.0:8000

# Vérifier les logs Django pour les requêtes
```

#### **4. Vérifier la base de données**
```bash
# Accéder au shell Django
python manage.py shell

# Vérifier les images en base
from image.models.image_model import ImageModel
print(f"Nombre d'images: {ImageModel.objects.count()}")
print("Premières images:", ImageModel.objects.all()[:5])
```

### **🚨 Causes possibles et solutions :**

#### **Cause 1 : URL API incorrecte**
**Problème :** `baseURL` pointe vers le mauvais serveur
```javascript
// ❌ Incorrect (URL relative)
baseURL: '/api/v1'

// ✅ Correct (URL absolue)
baseURL: 'http://localhost:8000/api/v1'
```

**Solution :** Vérifier `frontend/src/services/api.ts`

#### **Cause 2 : Serveur Django non démarré**
**Symptômes :** Erreur de connexion, CORS
**Solution :**
```bash
cd backend
python manage.py runserver 0.0.0.0:8000
```

#### **Cause 3 : Base de données vide**
**Symptômes :** API répond mais `count: 0`
**Solution :** Créer des images de test
```python
# Dans le shell Django
from image.models.image_model import ImageModel
from region.models.region_model import RegionModel
from datetime import date

region = RegionModel.objects.first()
ImageModel.objects.create(
    name="Test Image",
    capture_date=date.today(),
    satellite_source="SENTINEL_2",
    cloud_coverage=10.0,
    resolution=10.0,
    gee_asset_id="test_asset",
    region=region
)
```

#### **Cause 4 : Problème d'authentification**
**Symptômes :** Erreur 401, token invalide
**Solution :**
1. Vérifier le token dans localStorage
2. Se reconnecter
3. Vérifier l'expiration du token

#### **Cause 5 : Problème CORS**
**Symptômes :** Erreur CORS dans la console
**Solution :** Vérifier `settings.py` Django
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]
```

### **🧪 Tests de validation :**

#### **Test 1 : Connectivité serveur**
```bash
curl http://localhost:8000/
# Doit retourner une réponse Django
```

#### **Test 2 : Endpoint images**
```bash
curl -H "Authorization: Bearer TOKEN" http://localhost:8000/api/v1/images/
# Doit retourner JSON avec count et results
```

#### **Test 3 : Frontend**
```javascript
// Dans la console navigateur
fetch('http://localhost:8000/api/v1/images/', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
  }
}).then(r => r.json()).then(console.log);
```

### **📊 Réponse API attendue :**
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Image Bondoukou",
      "capture_date": "2024-01-15",
      "satellite_source": "SENTINEL_2",
      "region_name": "BONDOUKOU",
      "processing_status": "COMPLETED"
    }
  ]
}
```

### **🔧 Actions correctives :**

1. **Corriger l'URL API** dans `services/api.ts`
2. **Démarrer le serveur Django** sur le port 8000
3. **Créer des données de test** si la BD est vide
4. **Vérifier l'authentification** et les tokens
5. **Tester avec le bouton "Test API"** en mode développement

### **📞 Support :**

Si le problème persiste :
1. Capturer les logs de la console navigateur
2. Vérifier les logs du serveur Django
3. Tester l'API avec Postman ou curl
4. Vérifier les variables d'environnement `.env`

---

**🎯 Objectif :** API Images fonctionnelle retournant les données depuis Django vers React.
