/* Styles personnalisés pour Leaflet Maps */

/* Fix pour les icônes Leaflet manquantes */
.leaflet-default-icon-path {
  background-image: url('https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png');
}

/* Assurer que les cartes prennent toute la hauteur */
.leaflet-container {
  height: 100% !important;
  width: 100% !important;
  border-radius: 0.5rem;
}

/* Styles pour les marqueurs personnalisés */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

.custom-detection-marker {
  background: transparent !important;
  border: none !important;
}

/* Styles pour les popups */
.custom-popup .leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.alerts-popup .leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Styles pour les contrôles Leaflet */
.leaflet-control-layers {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.leaflet-control-zoom {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Styles spécifiques pour les cartes satellite */
.satellite-map .leaflet-control-layers {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.satellite-map .leaflet-control-layers label {
  color: white;
}

.satellite-map .leaflet-control-layers-separator {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Amélioration de la visibilité des contrôles sur fond satellite */
.leaflet-control-zoom a {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a:hover {
  background: rgba(255, 255, 255, 1);
}

/* Styles pour les outils de dessin */
.leaflet-draw-toolbar {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Assurer que les cartes sont visibles */
.leaflet-container .leaflet-tile {
  opacity: 1 !important;
}

/* Fix pour les problèmes de z-index */
.leaflet-control-container {
  z-index: 1000;
}

/* Styles pour les légendes et overlays */
.map-legend {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 12px;
  z-index: 1000;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .leaflet-control-layers {
    max-width: 200px;
  }

  .map-legend {
    max-width: 250px;
    font-size: 0.875rem;
  }
}
