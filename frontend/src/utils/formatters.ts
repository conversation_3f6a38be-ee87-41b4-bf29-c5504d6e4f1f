// Utilitaires de formatage pour l'application Gold Sentinel

/**
 * Formate un montant en devise avec les bonnes unités
 */
export const formatCurrency = (
  amount: number, 
  currency: string = 'EUR', 
  locale: string = 'fr-FR'
): string => {
  // Gestion des grands nombres avec unités appropriées
  if (Math.abs(amount) >= 1_000_000_000) {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(amount / 1_000_000_000) + ' Md';
  }
  
  if (Math.abs(amount) >= 1_000_000) {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(amount / 1_000_000) + ' M';
  }
  
  if (Math.abs(amount) >= 1_000) {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount / 1_000) + ' k';
  }
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Formate un nombre avec séparateurs de milliers
 */
export const formatNumber = (
  number: number, 
  locale: string = 'fr-FR',
  options?: Intl.NumberFormatOptions
): string => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  }).format(number);
};

/**
 * Formate un pourcentage
 */
export const formatPercentage = (
  value: number, 
  locale: string = 'fr-FR',
  decimals: number = 1
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
};

/**
 * Formate une surface en hectares avec unités appropriées
 */
export const formatArea = (
  hectares: number, 
  locale: string = 'fr-FR'
): string => {
  if (hectares >= 10_000) {
    return `${formatNumber(hectares / 10_000, locale, { maximumFractionDigits: 1 })} km²`;
  }
  
  if (hectares >= 1) {
    return `${formatNumber(hectares, locale, { maximumFractionDigits: 1 })} ha`;
  }
  
  return `${formatNumber(hectares * 10_000, locale, { maximumFractionDigits: 0 })} m²`;
};

/**
 * Formate une durée en minutes/heures/jours
 */
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${Math.round(minutes)} min`;
  }
  
  if (minutes < 1440) { // moins de 24h
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`;
  }
  
  const days = Math.floor(minutes / 1440);
  const remainingHours = Math.floor((minutes % 1440) / 60);
  return remainingHours > 0 ? `${days}j ${remainingHours}h` : `${days}j`;
};

/**
 * Formate une date relative (il y a X temps)
 */
export const formatRelativeTime = (date: Date | string): string => {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'À l\'instant';
  }
  
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `Il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
  
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
  }
  
  if (diffInSeconds < 2592000) { // moins de 30 jours
    const days = Math.floor(diffInSeconds / 86400);
    return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
  }
  
  if (diffInSeconds < 31536000) { // moins d'un an
    const months = Math.floor(diffInSeconds / 2592000);
    return `Il y a ${months} mois`;
  }
  
  const years = Math.floor(diffInSeconds / 31536000);
  return `Il y a ${years} an${years > 1 ? 's' : ''}`;
};

/**
 * Formate une date au format français
 */
export const formatDate = (
  date: Date | string, 
  options?: Intl.DateTimeFormatOptions
): string => {
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  }).format(targetDate);
};

/**
 * Formate une date et heure au format français
 */
export const formatDateTime = (
  date: Date | string, 
  options?: Intl.DateTimeFormatOptions
): string => {
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  }).format(targetDate);
};

/**
 * Formate un score de confiance
 */
export const formatConfidenceScore = (score: number): string => {
  const percentage = Math.round(score * 100);
  
  if (percentage >= 90) {
    return `${percentage}% (Très élevé)`;
  }
  
  if (percentage >= 75) {
    return `${percentage}% (Élevé)`;
  }
  
  if (percentage >= 50) {
    return `${percentage}% (Moyen)`;
  }
  
  if (percentage >= 25) {
    return `${percentage}% (Faible)`;
  }
  
  return `${percentage}% (Très faible)`;
};

/**
 * Formate un niveau de risque avec couleur
 */
export const formatRiskLevel = (level: string): {
  label: string;
  color: string;
  bgColor: string;
} => {
  switch (level.toUpperCase()) {
    case 'CRITICAL':
      return {
        label: 'Critique',
        color: 'text-red-700',
        bgColor: 'bg-red-100',
      };
    case 'HIGH':
      return {
        label: 'Élevé',
        color: 'text-orange-700',
        bgColor: 'bg-orange-100',
      };
    case 'MEDIUM':
      return {
        label: 'Moyen',
        color: 'text-yellow-700',
        bgColor: 'bg-yellow-100',
      };
    case 'LOW':
      return {
        label: 'Faible',
        color: 'text-green-700',
        bgColor: 'bg-green-100',
      };
    default:
      return {
        label: level,
        color: 'text-gray-700',
        bgColor: 'bg-gray-100',
      };
  }
};

/**
 * Formate des coordonnées GPS
 */
export const formatCoordinates = (
  latitude: number, 
  longitude: number, 
  precision: number = 6
): string => {
  const lat = latitude.toFixed(precision);
  const lng = longitude.toFixed(precision);
  const latDir = latitude >= 0 ? 'N' : 'S';
  const lngDir = longitude >= 0 ? 'E' : 'O';
  
  return `${Math.abs(parseFloat(lat))}°${latDir}, ${Math.abs(parseFloat(lng))}°${lngDir}`;
};

/**
 * Formate un nom de fichier pour téléchargement
 */
export const formatFileName = (
  baseName: string, 
  extension: string = 'pdf', 
  includeTimestamp: boolean = true
): string => {
  const sanitizedName = baseName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '');
  
  if (includeTimestamp) {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `${sanitizedName}_${timestamp}.${extension}`;
  }
  
  return `${sanitizedName}.${extension}`;
};

/**
 * Formate une taille de fichier
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

/**
 * Formate un statut avec couleur appropriée
 */
export const formatStatus = (status: string): {
  label: string;
  color: string;
  bgColor: string;
} => {
  switch (status.toUpperCase()) {
    case 'ACTIVE':
    case 'RUNNING':
    case 'IN_PROGRESS':
      return {
        label: 'Actif',
        color: 'text-blue-700',
        bgColor: 'bg-blue-100',
      };
    case 'COMPLETED':
    case 'RESOLVED':
    case 'SUCCESS':
      return {
        label: 'Terminé',
        color: 'text-green-700',
        bgColor: 'bg-green-100',
      };
    case 'FAILED':
    case 'ERROR':
      return {
        label: 'Échec',
        color: 'text-red-700',
        bgColor: 'bg-red-100',
      };
    case 'PENDING':
    case 'WAITING':
      return {
        label: 'En attente',
        color: 'text-yellow-700',
        bgColor: 'bg-yellow-100',
      };
    case 'CANCELLED':
    case 'DISMISSED':
      return {
        label: 'Annulé',
        color: 'text-gray-700',
        bgColor: 'bg-gray-100',
      };
    default:
      return {
        label: status,
        color: 'text-gray-700',
        bgColor: 'bg-gray-100',
      };
  }
};
