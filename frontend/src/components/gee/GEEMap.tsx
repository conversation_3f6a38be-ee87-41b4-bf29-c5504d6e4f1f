import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet-draw';
import {
  PlayIcon,
  MapIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { Card, Button, Badge, Loading } from '../ui';
import { usePermissions } from '../../hooks/usePermissions';
import geeService, { SpectralMapsData } from '../../services/gee.service';
import toast from 'react-hot-toast';

// Fix pour les icônes Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface LatLngBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

interface GEEMapProps {
  onAnalysisLaunch?: (bounds: LatLngBounds) => void;
  onDetectionSelect?: (detection: any) => void;
  detections?: any[];
  isAnalyzing?: boolean;
}

interface MapLayers {
  ndvi: boolean;
  ndwi: boolean;
  ndti: boolean;
  detections: boolean;
}

export const GEEMap: React.FC<GEEMapProps> = ({
  onAnalysisLaunch,
  onDetectionSelect,
  detections = [],
  isAnalyzing = false,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const drawControlRef = useRef<L.Control.Draw | null>(null);
  const drawnItemsRef = useRef<L.FeatureGroup | null>(null);
  const selectedAreaRef = useRef<L.Rectangle | null>(null);
  const markersRef = useRef<L.LayerGroup | null>(null);

  const permissions = usePermissions();

  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [selectedBounds, setSelectedBounds] = useState<LatLngBounds | null>(null);
  const [spectralData, setSpectralData] = useState<SpectralMapsData | null>(null);
  const [isLoadingSpectral, setIsLoadingSpectral] = useState(false);
  const [activeLayers, setActiveLayers] = useState<MapLayers>({
    ndvi: false,
    ndwi: false,
    ndti: false,
    detections: true,
  });

  // Initialiser Leaflet Map
  useEffect(() => {
    const initMap = () => {
      if (!mapRef.current) return;

      // Créer la carte centrée sur Bondoukou
      const map = L.map(mapRef.current).setView([8.0, -3.0], 10);

      // Ajouter les tuiles satellite (Esri World Imagery)
      L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri, Maxar, Earthstar Geographics, and the GIS User Community',
        maxZoom: 18,
      }).addTo(map);

      // Ajouter une couche de labels optionnelle
      const labelsLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri',
        maxZoom: 18,
      });

      mapInstanceRef.current = map;

      // Initialiser les groupes de couches
      const drawnItems = new L.FeatureGroup();
      map.addLayer(drawnItems);
      drawnItemsRef.current = drawnItems;

      // Initialiser le groupe de marqueurs pour les détections
      const markersGroup = new L.LayerGroup();
      map.addLayer(markersGroup);
      markersRef.current = markersGroup;

      // Initialiser le gestionnaire de dessin pour sélection de zone
      if (permissions.canLaunchAnalysis()) {
        const drawControl = new L.Control.Draw({
          position: 'topright',
          draw: {
            polygon: false,
            polyline: false,
            circle: false,
            marker: false,
            circlemarker: false,
            rectangle: {
              shapeOptions: {
                color: '#3B82F6',
                fillColor: '#3B82F6',
                fillOpacity: 0.2,
                weight: 2,
              },
            },
          },
          edit: {
            featureGroup: drawnItems,
            remove: true,
          },
        });

        map.addControl(drawControl);
        drawControlRef.current = drawControl;

        // Gérer la sélection de zone
        map.on(L.Draw.Event.CREATED, (event: any) => {
          const layer = event.layer;

          // Supprimer l'ancienne sélection
          drawnItems.clearLayers();

          // Ajouter la nouvelle sélection
          drawnItems.addLayer(layer);
          selectedAreaRef.current = layer;

          // Calculer les bounds et déclencher l'analyse
          if (layer instanceof L.Rectangle) {
            const bounds = layer.getBounds();
            const boundsObj: LatLngBounds = {
              north: bounds.getNorth(),
              south: bounds.getSouth(),
              east: bounds.getEast(),
              west: bounds.getWest(),
            };
            setSelectedBounds(boundsObj);
            onAnalysisLaunch?.(boundsObj);
          }
        });
      }

      // Ajouter les contrôles de couches
      const baseLayers = {
        '🛰️ Satellite': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri, Maxar, Earthstar Geographics, and the GIS User Community',
        }),
        '🗺️ OpenStreetMap': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
        }),
        '🌍 Terrain': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri, USGS, NOAA',
        }),
      };

      const overlayLayers = {
        '🏷️ Labels': labelsLayer,
      };

      L.control.layers(baseLayers, overlayLayers, {
        position: 'topright',
        collapsed: false
      }).addTo(map);

      setIsMapLoaded(true);
    };

    // Initialiser la carte directement
    initMap();

    return () => {
      // Cleanup
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [permissions]);

  // Afficher les détections sur la carte
  useEffect(() => {
    if (!mapInstanceRef.current || !markersRef.current || !activeLayers.detections) return;

    // Supprimer les anciens marqueurs
    markersRef.current.clearLayers();

    // Ajouter les nouveaux marqueurs
    detections.forEach((detection) => {
      const icon = getDetectionIcon(detection.detection_type, detection.confidence_score);

      const marker = L.marker([detection.latitude, detection.longitude], {
        icon: L.divIcon({
          className: 'custom-detection-marker',
          html: `<div style="background-color: ${icon.color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 10px;">${icon.symbol}</div>`,
          iconSize: [24, 24],
          iconAnchor: [12, 12],
        }),
        title: `Détection ${detection.id}`,
      });

      marker.on('click', () => {
        onDetectionSelect?.(detection);
      });

      markersRef.current?.addLayer(marker);
    });
  }, [detections, activeLayers.detections, onDetectionSelect]);

  const getDetectionIcon = (type: string, confidence: number) => {
    const color = confidence >= 0.8 ? '#EF4444' : confidence >= 0.5 ? '#F59E0B' : '#EAB308';
    const symbol = type === 'mining' ? 'M' : type === 'deforestation' ? 'D' : '?';
    return { color, symbol };
  };

  const handleLaunchAnalysis = () => {
    if (!selectedBounds) {
      toast.error('Veuillez sélectionner une zone sur la carte');
      return;
    }

    onAnalysisLaunch?.(selectedBounds);
  };

  const handleClearSelection = () => {
    if (drawnItemsRef.current) {
      drawnItemsRef.current.clearLayers();
    }
    selectedAreaRef.current = null;
    setSelectedBounds(null);
  };

  const toggleLayer = (layer: keyof MapLayers) => {
    setActiveLayers(prev => ({
      ...prev,
      [layer]: !prev[layer],
    }));
  };

  const loadSpectralLayers = async () => {
    if (!spectralData) {
      toast.error('Aucune donnée spectrale disponible');
      return;
    }

    setIsLoadingSpectral(true);
    try {
      const map = mapInstanceRef.current;
      if (!map) return;

      // Ajouter les couches spectrales comme overlays
      if (activeLayers.ndvi && spectralData.spectral_maps.ndvi_map_url) {
        // TODO: Implémenter l'ajout de couches GEE
        toast.info('Couche NDVI ajoutée');
      }

      if (activeLayers.ndwi && spectralData.spectral_maps.ndwi_map_url) {
        toast.info('Couche NDWI ajoutée');
      }

      if (activeLayers.ndti && spectralData.spectral_maps.ndti_map_url) {
        toast.info('Couche NDTI ajoutée');
      }
    } catch (error) {
      toast.error('Erreur lors du chargement des couches spectrales');
    } finally {
      setIsLoadingSpectral(false);
    }
  };

  return (
    <Card className="h-full flex flex-col">
      {/* Contrôles de la carte */}
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <MapIcon className="w-5 h-5 text-slate-600" />
            <h3 className="font-semibold text-slate-900">Carte d'Analyse Satellite</h3>
            {selectedBounds && (
              <Badge variant="blue" className="text-xs">
                Zone sélectionnée
              </Badge>
            )}
          </div>

          {permissions.canLaunchAnalysis() && (
            <div className="flex items-center space-x-2">
              {selectedBounds && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSelection}
                  className="text-slate-600"
                >
                  Effacer sélection
                </Button>
              )}
              <Button
                variant="primary"
                size="sm"
                onClick={handleLaunchAnalysis}
                disabled={!selectedBounds || isAnalyzing}
                loading={isAnalyzing}
                className="flex items-center space-x-1"
              >
                <PlayIcon className="w-4 h-4" />
                <span>Lancer Analyse</span>
              </Button>
            </div>
          )}
        </div>

        {/* Contrôles des couches */}
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-slate-700">Couches :</span>

          <button
            onClick={() => toggleLayer('detections')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.detections
                ? 'bg-blue-100 text-blue-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.detections ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>Détections</span>
          </button>

          <button
            onClick={() => toggleLayer('ndvi')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.ndvi
                ? 'bg-green-100 text-green-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.ndvi ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>NDVI</span>
          </button>

          <button
            onClick={() => toggleLayer('ndwi')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.ndwi
                ? 'bg-blue-100 text-blue-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.ndwi ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>NDWI</span>
          </button>

          <button
            onClick={() => toggleLayer('ndti')}
            className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
              activeLayers.ndti
                ? 'bg-orange-100 text-orange-800'
                : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
            }`}
          >
            {activeLayers.ndti ? <EyeIcon className="w-3 h-3" /> : <EyeSlashIcon className="w-3 h-3" />}
            <span>NDTI</span>
          </button>

          {spectralData && (
            <Button
              variant="ghost"
              size="sm"
              onClick={loadSpectralLayers}
              loading={isLoadingSpectral}
              className="flex items-center space-x-1"
            >
              <ArrowPathIcon className="w-3 h-3" />
              <span>Actualiser couches</span>
            </Button>
          )}
        </div>
      </div>

      {/* Carte */}
      <div className="flex-1 relative">
        <div ref={mapRef} className="w-full h-full" />

        {!isMapLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-100">
            <Loading />
          </div>
        )}

        {/* Instructions */}
        {isMapLoaded && permissions.canLaunchAnalysis() && !selectedBounds && (
          <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 border max-w-xs">
            <p className="text-sm text-slate-600">
              <strong>Instructions :</strong><br />
              1. Utilisez l'outil rectangle pour sélectionner une zone<br />
              2. Cliquez sur "Lancer Analyse" pour démarrer
            </p>
          </div>
        )}

        {/* Informations de la zone sélectionnée */}
        {selectedBounds && (
          <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 border">
            <h4 className="text-sm font-semibold text-slate-900 mb-2">Zone sélectionnée</h4>
            <div className="text-xs text-slate-600 space-y-1">
              <div>Nord: {selectedBounds.north.toFixed(6)}</div>
              <div>Sud: {selectedBounds.south.toFixed(6)}</div>
              <div>Est: {selectedBounds.east.toFixed(6)}</div>
              <div>Ouest: {selectedBounds.west.toFixed(6)}</div>
            </div>
          </div>
        )}
      </div>

      {/* Légende */}
      <div className="p-3 border-t border-slate-200 bg-slate-50">
        <div className="flex items-center justify-between text-xs text-slate-500">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span>Haute confiance</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>Moyenne confiance</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span>Faible confiance</span>
            </div>
          </div>
          <span>{detections.length} détection(s) affichée(s)</span>
        </div>
      </div>
    </Card>
  );
};
