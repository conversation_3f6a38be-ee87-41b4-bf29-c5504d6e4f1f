import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { XMarkIcon } from '@heroicons/react/24/outline';
import investigationService from '../services/investigation.service';
import { Button } from './ui';
import toast from 'react-hot-toast';

interface CreateInvestigationModalProps {
  isOpen: boolean;
  onClose: () => void;
  detectionId: number;
  detectionInfo?: {
    type: string;
    region: string;
    coordinates: string;
  };
}

export const CreateInvestigationModal: React.FC<CreateInvestigationModalProps> = ({
  isOpen,
  onClose,
  detectionId,
  detectionInfo
}) => {
  const [accessInstructions, setAccessInstructions] = useState('');
  const [assignedTo, setAssignedTo] = useState<number | undefined>(undefined);
  const queryClient = useQueryClient();

  // Récupérer les agents disponibles
  const { data: agentsData, isLoading: agentsLoading } = useQuery({
    queryKey: ['available-agents'],
    queryFn: () => investigationService.getAvailableAgents(),
    enabled: isOpen
  });

  // Mutation pour créer l'investigation
  const createMutation = useMutation({
    mutationFn: (data: { detection_id: number; access_instructions?: string; assigned_to?: number }) =>
      investigationService.createInvestigation(data),
    onSuccess: (response) => {
      toast.success(response.message || 'Investigation créée avec succès');
      queryClient.invalidateQueries({ queryKey: ['investigations'] });
      queryClient.invalidateQueries({ queryKey: ['pending-investigations'] });
      onClose();
      resetForm();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    }
  });

  const resetForm = () => {
    setAccessInstructions('');
    setAssignedTo(undefined);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const data: any = {
      detection_id: detectionId,
    };

    if (accessInstructions.trim()) {
      data.access_instructions = accessInstructions.trim();
    }

    if (assignedTo) {
      data.assigned_to = assignedTo;
    }

    createMutation.mutate(data);
  };

  const handleClose = () => {
    if (!createMutation.isPending) {
      onClose();
      resetForm();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* En-tête */}
        <div className="flex items-center justify-between p-6 border-b border-slate-200">
          <h2 className="text-xl font-semibold text-slate-900">
            Créer une investigation
          </h2>
          <button
            onClick={handleClose}
            disabled={createMutation.isPending}
            className="text-slate-400 hover:text-slate-600 disabled:opacity-50"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Contenu */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Informations de la détection */}
          {detectionInfo && (
            <div className="mb-6 p-4 bg-slate-50 rounded-lg">
              <h3 className="font-medium text-slate-900 mb-2">Détection #{detectionId}</h3>
              <div className="space-y-1 text-sm text-slate-600">
                <p><span className="font-medium">Type:</span> {detectionInfo.type}</p>
                <p><span className="font-medium">Région:</span> {detectionInfo.region}</p>
                <p><span className="font-medium">Coordonnées:</span> {detectionInfo.coordinates}</p>
              </div>
            </div>
          )}

          {/* Instructions d'accès */}
          <div className="mb-6">
            <label htmlFor="access-instructions" className="block text-sm font-medium text-slate-700 mb-2">
              Instructions d'accès (optionnel)
            </label>
            <textarea
              id="access-instructions"
              value={accessInstructions}
              onChange={(e) => setAccessInstructions(e.target.value)}
              placeholder="Décrivez comment accéder au site de la détection..."
              rows={4}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
          </div>

          {/* Assignation directe */}
          <div className="mb-6">
            <label htmlFor="assigned-to" className="block text-sm font-medium text-slate-700 mb-2">
              Assigner à un agent (optionnel)
            </label>
            {agentsLoading ? (
              <div className="text-sm text-slate-500">Chargement des agents...</div>
            ) : (
              <select
                id="assigned-to"
                value={assignedTo || ''}
                onChange={(e) => setAssignedTo(e.target.value ? Number(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Assigner plus tard</option>
                {agentsData?.agents?.map((agent) => (
                  <option key={agent.id} value={agent.id}>
                    {agent.full_name} ({agent.availability_status} - {agent.total_workload} investigations)
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createMutation.isPending}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending}
              className="min-w-[120px]"
            >
              {createMutation.isPending ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Création...</span>
                </div>
              ) : (
                'Créer'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
