import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CalendarIcon,
  PlusIcon,
  PlayIcon,
  PauseIcon,
  TrashIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import analysisService from '../../services/analysis.service';
import type { AnalysisSchedule, AnalysisTemplate } from '../../services/analysis.service';
import { Card, Button, Loading } from '../ui';
import { formatDateTime, formatDate } from '../../utils/formatters';
import toast from 'react-hot-toast';

interface AnalysisScheduleManagerProps {
  schedules: AnalysisSchedule[];
  templates: AnalysisTemplate[];
  onScheduleChange: () => void;
}

export const AnalysisScheduleManager: React.FC<AnalysisScheduleManagerProps> = ({
  schedules,
  templates,
  onScheduleChange,
}) => {
  const [selectedSchedule, setSelectedSchedule] = useState<AnalysisSchedule | null>(null);

  // Mutations
  const toggleScheduleMutation = useMutation({
    mutationFn: ({ scheduleId, isActive }: { scheduleId: string; isActive: boolean }) =>
      analysisService.toggleAnalysisSchedule(scheduleId, isActive),
    onSuccess: () => {
      onScheduleChange();
      toast.success('Programmation mise à jour');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la mise à jour');
    }
  });

  const deleteScheduleMutation = useMutation({
    mutationFn: (scheduleId: string) => analysisService.deleteAnalysisSchedule(scheduleId),
    onSuccess: () => {
      onScheduleChange();
      toast.success('Programmation supprimée');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la suppression');
    }
  });

  const handleToggleSchedule = (schedule: AnalysisSchedule) => {
    toggleScheduleMutation.mutate({
      scheduleId: schedule.id,
      isActive: !schedule.is_active
    });
  };

  const handleDeleteSchedule = (schedule: AnalysisSchedule) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la programmation "${schedule.name}" ?`)) {
      deleteScheduleMutation.mutate(schedule.id);
    }
  };

  const getCronDescription = (cronExpression: string): string => {
    // Simplification pour l'exemple - dans un vrai projet, utiliser une librairie comme cronstrue
    const parts = cronExpression.split(' ');
    if (parts.length >= 5) {
      const minute = parts[0];
      const hour = parts[1];
      const day = parts[2];
      const month = parts[3];
      const dayOfWeek = parts[4];

      if (minute === '0' && hour === '0' && day === '*' && month === '*' && dayOfWeek === '*') {
        return 'Tous les jours à minuit';
      }
      if (minute === '0' && hour === '0' && day === '*' && month === '*' && dayOfWeek === '1') {
        return 'Tous les lundis à minuit';
      }
      if (minute === '0' && hour === '0' && day === '1' && month === '*' && dayOfWeek === '*') {
        return 'Le 1er de chaque mois à minuit';
      }
    }
    return cronExpression;
  };

  const getTemplateById = (templateId: string) => {
    return templates.find(t => t.id === templateId);
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              Gestionnaire de Programmations
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Programmez des analyses automatiques selon vos besoins
            </p>
          </div>
          <Button
            onClick={() => toast.info('Fonctionnalité à implémenter')}
            className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white"
          >
            <PlusIcon className="w-4 h-4" />
            <span>Nouvelle Programmation</span>
          </Button>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-sm text-blue-600">Total programmations</div>
            <div className="text-2xl font-bold text-blue-900">{schedules.length}</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-sm text-green-600">Actives</div>
            <div className="text-2xl font-bold text-green-900">
              {schedules.filter(s => s.is_active).length}
            </div>
          </div>
          <div className="bg-orange-50 rounded-lg p-4">
            <div className="text-sm text-orange-600">Prochaines exécutions</div>
            <div className="text-2xl font-bold text-orange-900">
              {schedules.filter(s => s.is_active && new Date(s.next_run) > new Date()).length}
            </div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-sm text-purple-600">Templates utilisés</div>
            <div className="text-2xl font-bold text-purple-900">
              {new Set(schedules.map(s => s.template_id)).size}
            </div>
          </div>
        </div>
      </Card>

      {/* Liste des programmations */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Programmations configurées ({schedules.length})
          </h3>
        </div>

        {schedules.length === 0 ? (
          <div className="text-center py-12">
            <CalendarIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune programmation
            </h3>
            <p className="text-gray-500 mb-4">
              Créez votre première programmation d'analyse automatique.
            </p>
            <Button
              onClick={() => toast.info('Fonctionnalité à implémenter')}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Créer une programmation
            </Button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            <AnimatePresence>
              {schedules.map((schedule, index) => {
                const template = getTemplateById(schedule.template_id);
                const nextRun = new Date(schedule.next_run);
                const isUpcoming = nextRun > new Date();
                
                return (
                  <motion.div
                    key={schedule.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="p-6 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-medium text-gray-900">
                            {schedule.name}
                          </h4>
                          <div className="flex items-center space-x-2">
                            {schedule.is_active ? (
                              <span className="flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                <CheckCircleIcon className="w-3 h-3 mr-1" />
                                Active
                              </span>
                            ) : (
                              <span className="flex items-center px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                                <XCircleIcon className="w-3 h-3 mr-1" />
                                Inactive
                              </span>
                            )}
                            {isUpcoming && schedule.is_active && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                                Programmée
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                          <div>
                            <span className="text-sm text-gray-600">Template :</span>
                            <div className="font-medium text-gray-900">
                              {template ? template.name : 'Template introuvable'}
                            </div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-600">Fréquence :</span>
                            <div className="font-medium text-gray-900">
                              {getCronDescription(schedule.cron_expression)}
                            </div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-600">Prochaine exécution :</span>
                            <div className={`font-medium ${
                              isUpcoming && schedule.is_active ? 'text-blue-600' : 'text-gray-900'
                            }`}>
                              {formatDateTime(schedule.next_run)}
                            </div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-600">Dernière exécution :</span>
                            <div className="font-medium text-gray-900">
                              {schedule.last_run ? formatDateTime(schedule.last_run) : 'Jamais'}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>Créé par {schedule.created_by_name}</span>
                          <span>•</span>
                          <span>Expression cron : {schedule.cron_expression}</span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleToggleSchedule(schedule)}
                          disabled={toggleScheduleMutation.isPending}
                          className={`p-2 rounded-full transition-colors ${
                            schedule.is_active
                              ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-100'
                              : 'text-green-600 hover:text-green-800 hover:bg-green-100'
                          }`}
                          title={schedule.is_active ? 'Désactiver' : 'Activer'}
                        >
                          {toggleScheduleMutation.isPending ? (
                            <Loading size="sm" />
                          ) : schedule.is_active ? (
                            <PauseIcon className="w-4 h-4" />
                          ) : (
                            <PlayIcon className="w-4 h-4" />
                          )}
                        </button>

                        <button
                          onClick={() => toast.info('Fonctionnalité à implémenter')}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors"
                          title="Exécuter maintenant"
                        >
                          <ClockIcon className="w-4 h-4" />
                        </button>

                        <button
                          onClick={() => handleDeleteSchedule(schedule)}
                          disabled={deleteScheduleMutation.isPending}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full transition-colors disabled:opacity-50"
                          title="Supprimer"
                        >
                          {deleteScheduleMutation.isPending ? (
                            <Loading size="sm" />
                          ) : (
                            <TrashIcon className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        )}
      </Card>

      {/* Prochaines exécutions */}
      {schedules.filter(s => s.is_active).length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Prochaines Exécutions
          </h3>
          <div className="space-y-3">
            {schedules
              .filter(s => s.is_active)
              .sort((a, b) => new Date(a.next_run).getTime() - new Date(b.next_run).getTime())
              .slice(0, 5)
              .map((schedule) => {
                const template = getTemplateById(schedule.template_id);
                const nextRun = new Date(schedule.next_run);
                const isUpcoming = nextRun > new Date();
                
                return (
                  <div
                    key={schedule.id}
                    className={`flex items-center justify-between p-3 rounded-lg ${
                      isUpcoming ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <CalendarIcon className={`w-5 h-5 ${
                        isUpcoming ? 'text-blue-600' : 'text-gray-400'
                      }`} />
                      <div>
                        <div className="font-medium text-gray-900">{schedule.name}</div>
                        <div className="text-sm text-gray-600">
                          {template ? template.name : 'Template introuvable'}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${
                        isUpcoming ? 'text-blue-600' : 'text-gray-900'
                      }`}>
                        {formatDateTime(schedule.next_run)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {getCronDescription(schedule.cron_expression)}
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </Card>
      )}
    </div>
  );
};
