import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  PlayIcon,
  MapPinIcon,
  BeakerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import analysisService from '../../services/analysis.service';
import type { AnalysisStats } from '../../services/analysis.service';
import { Card, Button, Loading } from '../ui';
import { formatNumber, formatDuration } from '../../utils/formatters';
import toast from 'react-hot-toast';

interface BulkAnalysisRunnerProps {
  analysisStats?: AnalysisStats;
  onBulkRun: () => void;
}

export const BulkAnalysisRunner: React.FC<BulkAnalysisRunnerProps> = ({
  analysisStats,
  onBulkRun,
}) => {
  const [selectedZones, setSelectedZones] = useState<number[]>([]);
  const [analysisType, setAnalysisType] = useState<'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS'>('MINING_DETECTION');
  const [priority, setPriority] = useState<'LOW' | 'MEDIUM' | 'HIGH'>('MEDIUM');

  // Récupération des zones disponibles
  const { data: zones, isLoading: zonesLoading } = useQuery({
    queryKey: ['analysis-zones'],
    queryFn: () => analysisService.getAnalysisZones(),
  });

  // Mutation pour lancement en lot
  const bulkAnalysisMutation = useMutation({
    mutationFn: () => analysisService.runBulkAnalysis(selectedZones, analysisType, priority),
    onSuccess: (data) => {
      onBulkRun();
      toast.success(`${data.jobs_created.length} analyses lancées avec succès`);
      setSelectedZones([]);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du lancement des analyses');
    }
  });

  const handleZoneToggle = (zoneId: number) => {
    setSelectedZones(prev => 
      prev.includes(zoneId)
        ? prev.filter(id => id !== zoneId)
        : [...prev, zoneId]
    );
  };

  const handleSelectAllZones = () => {
    if (zones) {
      setSelectedZones(zones.map(zone => zone.id));
    }
  };

  const handleDeselectAllZones = () => {
    setSelectedZones([]);
  };

  const handleLaunchBulkAnalysis = () => {
    if (selectedZones.length === 0) {
      toast.error('Veuillez sélectionner au moins une zone');
      return;
    }
    bulkAnalysisMutation.mutate();
  };

  const getAnalysisTypeInfo = () => {
    switch (analysisType) {
      case 'MINING_DETECTION':
        return {
          name: 'Détection minière',
          description: 'Détection automatique des sites d\'extraction d\'or',
          icon: BeakerIcon,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
        };
      case 'CHANGE_DETECTION':
        return {
          name: 'Détection de changements',
          description: 'Analyse comparative pour identifier les modifications',
          icon: ExclamationTriangleIcon,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
        };
      case 'VEGETATION_ANALYSIS':
        return {
          name: 'Analyse de végétation',
          description: 'Évaluation de la santé de la couverture végétale',
          icon: CheckCircleIcon,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
        };
    }
  };

  const typeInfo = getAnalysisTypeInfo();
  const IconComponent = typeInfo.icon;

  const estimatedDuration = selectedZones.length * 15; // 15 minutes par zone
  const estimatedCost = selectedZones.length * 2.5; // 2.5€ par zone

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <Card className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <ChartBarIcon className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              Analyses en Lot
            </h2>
            <p className="text-sm text-gray-600">
              Lancez des analyses sur plusieurs zones simultanément pour une couverture complète
            </p>
          </div>
        </div>

        {/* Statistiques globales */}
        {analysisStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-sm text-blue-600">Analyses totales</div>
              <div className="text-2xl font-bold text-blue-900">
                {formatNumber(analysisStats.total_analyses)}
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-sm text-green-600">Taux de réussite</div>
              <div className="text-2xl font-bold text-green-900">
                {((analysisStats.completed_analyses / analysisStats.total_analyses) * 100).toFixed(1)}%
              </div>
            </div>
            <div className="bg-orange-50 rounded-lg p-4">
              <div className="text-sm text-orange-600">Surface analysée</div>
              <div className="text-2xl font-bold text-orange-900">
                {(analysisStats.total_area_analyzed_hectares / 1000).toFixed(1)}k ha
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-sm text-purple-600">Détections</div>
              <div className="text-2xl font-bold text-purple-900">
                {formatNumber(analysisStats.total_detections)}
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Configuration de l'analyse */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Configuration de l'Analyse en Lot
        </h3>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Type d'analyse */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Type d'analyse</h4>
            <div className="space-y-3">
              {(['MINING_DETECTION', 'CHANGE_DETECTION', 'VEGETATION_ANALYSIS'] as const).map((type) => {
                const info = type === 'MINING_DETECTION' ? 
                  { name: 'Détection minière', icon: BeakerIcon, color: 'orange' } :
                  type === 'CHANGE_DETECTION' ?
                  { name: 'Détection changements', icon: ExclamationTriangleIcon, color: 'blue' } :
                  { name: 'Analyse végétation', icon: CheckCircleIcon, color: 'green' };
                
                const Icon = info.icon;
                
                return (
                  <motion.div
                    key={type}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      analysisType === type
                        ? `border-${info.color}-500 bg-${info.color}-50`
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setAnalysisType(type)}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className={`w-5 h-5 ${analysisType === type ? `text-${info.color}-600` : 'text-gray-400'}`} />
                      <div>
                        <h5 className="font-medium text-gray-900">{info.name}</h5>
                        <p className="text-sm text-gray-600">
                          {type === 'MINING_DETECTION' ? 'Détection sites d\'extraction' :
                           type === 'CHANGE_DETECTION' ? 'Analyse comparative temporelle' :
                           'Évaluation couverture végétale'}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Priorité */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Priorité d'exécution</h4>
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value as 'LOW' | 'MEDIUM' | 'HIGH')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 mb-4"
            >
              <option value="LOW">Faible (traitement différé)</option>
              <option value="MEDIUM">Moyenne (traitement normal)</option>
              <option value="HIGH">Élevée (traitement prioritaire)</option>
            </select>

            {/* Estimation */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h5 className="font-medium text-gray-900 mb-2">Estimation</h5>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Zones sélectionnées :</span>
                  <span className="font-medium">{selectedZones.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Durée estimée :</span>
                  <span className="font-medium">{formatDuration(estimatedDuration)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Coût estimé :</span>
                  <span className="font-medium">{estimatedCost.toFixed(2)} €</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Sélection des zones */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Sélection des Zones ({selectedZones.length} sélectionnée{selectedZones.length > 1 ? 's' : ''})
          </h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAllZones}
              disabled={zonesLoading}
            >
              Tout sélectionner
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeselectAllZones}
              disabled={selectedZones.length === 0}
            >
              Tout désélectionner
            </Button>
          </div>
        </div>

        {zonesLoading ? (
          <div className="flex justify-center py-8">
            <Loading />
          </div>
        ) : zones && zones.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {zones.map((zone) => (
              <motion.div
                key={zone.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedZones.includes(zone.id)
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleZoneToggle(zone.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{zone.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{zone.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>{zone.area_hectares.toFixed(1)} ha</span>
                      <span>•</span>
                      <span>{zone.region}</span>
                    </div>
                  </div>
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedZones.includes(zone.id)
                      ? 'border-purple-500 bg-purple-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedZones.includes(zone.id) && (
                      <CheckCircleIcon className="w-3 h-3 text-white" />
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <MapPinIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune zone disponible
            </h3>
            <p className="text-gray-500">
              Aucune zone d'analyse n'est actuellement configurée.
            </p>
          </div>
        )}
      </Card>

      {/* Résumé et lancement */}
      {selectedZones.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Résumé de l'Analyse en Lot
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className={`p-4 rounded-lg ${typeInfo.bgColor}`}>
              <div className="flex items-center space-x-3">
                <IconComponent className={`w-6 h-6 ${typeInfo.color}`} />
                <div>
                  <h4 className="font-medium text-gray-900">{typeInfo.name}</h4>
                  <p className="text-sm text-gray-600">{selectedZones.length} zone{selectedZones.length > 1 ? 's' : ''}</p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <ClockIcon className="w-6 h-6 text-blue-600" />
                <div>
                  <h4 className="font-medium text-gray-900">Durée estimée</h4>
                  <p className="text-sm text-gray-600">{formatDuration(estimatedDuration)}</p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <DocumentTextIcon className="w-6 h-6 text-green-600" />
                <div>
                  <h4 className="font-medium text-gray-900">Priorité</h4>
                  <p className="text-sm text-gray-600">
                    {priority === 'HIGH' ? 'Élevée' : priority === 'MEDIUM' ? 'Moyenne' : 'Faible'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-900">Attention</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Cette opération lancera {selectedZones.length} analyse{selectedZones.length > 1 ? 's' : ''} simultanément. 
                  Assurez-vous que les ressources de traitement sont suffisantes.
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleDeselectAllZones}
              disabled={bulkAnalysisMutation.isPending}
            >
              Réinitialiser
            </Button>
            <Button
              onClick={handleLaunchBulkAnalysis}
              disabled={bulkAnalysisMutation.isPending || selectedZones.length === 0}
              className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white"
            >
              {bulkAnalysisMutation.isPending ? (
                <>
                  <Loading size="sm" />
                  <span>Lancement...</span>
                </>
              ) : (
                <>
                  <PlayIcon className="w-4 h-4" />
                  <span>Lancer {selectedZones.length} analyse{selectedZones.length > 1 ? 's' : ''}</span>
                </>
              )}
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};
