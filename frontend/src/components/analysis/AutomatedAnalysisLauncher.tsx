import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  PlayIcon,
  MapPinIcon,
  BeakerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import analysisService from '../../services/analysis.service';
import type { AutomatedAnalysisRun, AnalysisZone } from '../../services/analysis.service';
import { Card, Button, Loading } from '../ui';

interface AutomatedAnalysisLauncherProps {
  onLaunch: (config: AutomatedAnalysisRun) => void;
  isLoading?: boolean;
}

export const AutomatedAnalysisLauncher: React.FC<AutomatedAnalysisLauncherProps> = ({
  onLaunch,
  isLoading = false,
}) => {
  // États du formulaire
  const [selectedZones, setSelectedZones] = useState<number[]>([]);
  const [analysisType, setAnalysisType] = useState<'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS'>('MINING_DETECTION');
  const [priority, setPriority] = useState<'LOW' | 'MEDIUM' | 'HIGH'>('MEDIUM');
  const [includeFinancialAssessment, setIncludeFinancialAssessment] = useState(true);
  const [notificationSettings, setNotificationSettings] = useState({
    email_on_completion: true,
    email_on_failure: true,
    alert_on_high_risk: true,
  });

  // Récupération des zones disponibles
  const { data: zones, isLoading: zonesLoading } = useQuery({
    queryKey: ['analysis-zones'],
    queryFn: () => analysisService.getAnalysisZones(),
  });

  const handleZoneToggle = (zoneId: number) => {
    setSelectedZones(prev => 
      prev.includes(zoneId)
        ? prev.filter(id => id !== zoneId)
        : [...prev, zoneId]
    );
  };

  const handleSelectAllZones = () => {
    if (zones) {
      setSelectedZones(zones.map(zone => zone.id));
    }
  };

  const handleDeselectAllZones = () => {
    setSelectedZones([]);
  };

  const handleLaunch = () => {
    if (selectedZones.length === 0) {
      return;
    }

    const config: AutomatedAnalysisRun = {
      zone_ids: selectedZones,
      analysis_type: analysisType,
      priority,
      include_financial_assessment: includeFinancialAssessment,
      notification_settings: notificationSettings,
    };

    onLaunch(config);
  };

  const getAnalysisTypeInfo = () => {
    switch (analysisType) {
      case 'MINING_DETECTION':
        return {
          name: 'Détection d\'activité minière',
          description: 'Détection automatique des sites d\'extraction d\'or et évaluation de leur impact',
          icon: BeakerIcon,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
        };
      case 'CHANGE_DETECTION':
        return {
          name: 'Détection de changements',
          description: 'Analyse comparative pour identifier les modifications du paysage',
          icon: ExclamationTriangleIcon,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
        };
      case 'VEGETATION_ANALYSIS':
        return {
          name: 'Analyse de végétation',
          description: 'Évaluation de la santé et de la densité de la couverture végétale',
          icon: CheckCircleIcon,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
        };
    }
  };

  const typeInfo = getAnalysisTypeInfo();
  const IconComponent = typeInfo.icon;

  const estimatedDuration = selectedZones.length * 15; // 15 minutes par zone
  const estimatedCost = selectedZones.length * 2.5; // 2.5€ par zone

  return (
    <div className="space-y-6">
      {/* Configuration principale */}
      <Card className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <PlayIcon className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              Lancement d'Analyse Automatisée
            </h2>
            <p className="text-sm text-gray-600">
              Configurez et lancez des analyses sur plusieurs zones simultanément
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sélection du type d'analyse */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Type d'analyse</h3>
            <div className="space-y-3">
              {(['MINING_DETECTION', 'CHANGE_DETECTION', 'VEGETATION_ANALYSIS'] as const).map((type) => {
                const info = type === 'MINING_DETECTION' ? 
                  { name: 'Détection minière', icon: BeakerIcon, color: 'orange' } :
                  type === 'CHANGE_DETECTION' ?
                  { name: 'Détection changements', icon: ExclamationTriangleIcon, color: 'blue' } :
                  { name: 'Analyse végétation', icon: CheckCircleIcon, color: 'green' };
                
                const Icon = info.icon;
                
                return (
                  <motion.div
                    key={type}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      analysisType === type
                        ? `border-${info.color}-500 bg-${info.color}-50`
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setAnalysisType(type)}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className={`w-5 h-5 ${analysisType === type ? `text-${info.color}-600` : 'text-gray-400'}`} />
                      <div>
                        <h4 className="font-medium text-gray-900">{info.name}</h4>
                        <p className="text-sm text-gray-600">
                          {type === 'MINING_DETECTION' ? 'Détection sites d\'extraction' :
                           type === 'CHANGE_DETECTION' ? 'Analyse comparative temporelle' :
                           'Évaluation couverture végétale'}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Configuration avancée */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration</h3>
            <div className="space-y-4">
              {/* Priorité */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priorité d'exécution
                </label>
                <select
                  value={priority}
                  onChange={(e) => setPriority(e.target.value as 'LOW' | 'MEDIUM' | 'HIGH')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="LOW">Faible (traitement différé)</option>
                  <option value="MEDIUM">Moyenne (traitement normal)</option>
                  <option value="HIGH">Élevée (traitement prioritaire)</option>
                </select>
              </div>

              {/* Évaluation financière */}
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="financial-assessment"
                  checked={includeFinancialAssessment}
                  onChange={(e) => setIncludeFinancialAssessment(e.target.checked)}
                  className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                />
                <label htmlFor="financial-assessment" className="text-sm font-medium text-gray-700">
                  Inclure l'évaluation des risques financiers
                </label>
              </div>

              {/* Notifications */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notifications
                </label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="email-completion"
                      checked={notificationSettings.email_on_completion}
                      onChange={(e) => setNotificationSettings(prev => ({
                        ...prev,
                        email_on_completion: e.target.checked
                      }))}
                      className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                    />
                    <label htmlFor="email-completion" className="text-sm text-gray-700">
                      Email à la fin de l'analyse
                    </label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="email-failure"
                      checked={notificationSettings.email_on_failure}
                      onChange={(e) => setNotificationSettings(prev => ({
                        ...prev,
                        email_on_failure: e.target.checked
                      }))}
                      className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                    />
                    <label htmlFor="email-failure" className="text-sm text-gray-700">
                      Email en cas d'échec
                    </label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="alert-high-risk"
                      checked={notificationSettings.alert_on_high_risk}
                      onChange={(e) => setNotificationSettings(prev => ({
                        ...prev,
                        alert_on_high_risk: e.target.checked
                      }))}
                      className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                    />
                    <label htmlFor="alert-high-risk" className="text-sm text-gray-700">
                      Alerte si risque élevé détecté
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Sélection des zones */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Sélection des zones ({selectedZones.length} sélectionnée{selectedZones.length > 1 ? 's' : ''})
          </h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAllZones}
              disabled={zonesLoading}
            >
              Tout sélectionner
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeselectAllZones}
              disabled={selectedZones.length === 0}
            >
              Tout désélectionner
            </Button>
          </div>
        </div>

        {zonesLoading ? (
          <div className="flex justify-center py-8">
            <Loading />
          </div>
        ) : zones && zones.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {zones.map((zone) => (
              <motion.div
                key={zone.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedZones.includes(zone.id)
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleZoneToggle(zone.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{zone.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{zone.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>{zone.area_hectares.toFixed(1)} ha</span>
                      <span>•</span>
                      <span>{zone.region}</span>
                    </div>
                  </div>
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedZones.includes(zone.id)
                      ? 'border-purple-500 bg-purple-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedZones.includes(zone.id) && (
                      <CheckCircleIcon className="w-3 h-3 text-white" />
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <MapPinIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune zone disponible
            </h3>
            <p className="text-gray-500">
              Aucune zone d'analyse n'est actuellement configurée.
            </p>
          </div>
        )}
      </Card>

      {/* Résumé et lancement */}
      {selectedZones.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Résumé de l'analyse</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className={`p-4 rounded-lg ${typeInfo.bgColor}`}>
              <div className="flex items-center space-x-3">
                <IconComponent className={`w-6 h-6 ${typeInfo.color}`} />
                <div>
                  <h4 className="font-medium text-gray-900">{typeInfo.name}</h4>
                  <p className="text-sm text-gray-600">{selectedZones.length} zone{selectedZones.length > 1 ? 's' : ''}</p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <ClockIcon className="w-6 h-6 text-blue-600" />
                <div>
                  <h4 className="font-medium text-gray-900">Durée estimée</h4>
                  <p className="text-sm text-gray-600">~{estimatedDuration} minutes</p>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <BellIcon className="w-6 h-6 text-green-600" />
                <div>
                  <h4 className="font-medium text-gray-900">Notifications</h4>
                  <p className="text-sm text-gray-600">
                    {Object.values(notificationSettings).filter(Boolean).length} activée{Object.values(notificationSettings).filter(Boolean).length > 1 ? 's' : ''}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setSelectedZones([])}
              disabled={isLoading}
            >
              Réinitialiser
            </Button>
            <Button
              onClick={handleLaunch}
              disabled={isLoading || selectedZones.length === 0}
              className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white"
            >
              {isLoading ? (
                <>
                  <Loading size="sm" />
                  <span>Lancement...</span>
                </>
              ) : (
                <>
                  <PlayIcon className="w-4 h-4" />
                  <span>Lancer l'analyse ({selectedZones.length} zone{selectedZones.length > 1 ? 's' : ''})</span>
                </>
              )}
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};
