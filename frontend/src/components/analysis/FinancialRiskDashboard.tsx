import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ChartBarIcon,
  DocumentTextIcon,
  MapPinIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import type { RiskDashboardData } from '../../services/financial-risk.service';
import { Card, Button, Loading } from '../ui';
import { formatCurrency } from '../../utils/formatters';

interface FinancialRiskDashboardProps {
  dashboardData?: RiskDashboardData;
  isLoading?: boolean;
  period: 'week' | 'month' | 'quarter' | 'year';
}

export const FinancialRiskDashboard: React.FC<FinancialRiskDashboardProps> = ({
  dashboardData,
  isLoading = false,
  period,
}) => {
  const [selectedScenario, setSelectedScenario] = useState<'current' | 'optimistic' | 'pessimistic'>('current');

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <Loading />
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <Card className="p-8 text-center">
        <ExclamationTriangleIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Données non disponibles
        </h3>
        <p className="text-gray-500">
          Les données de risques financiers ne sont pas encore disponibles.
        </p>
      </Card>
    );
  }

  const getPeriodLabel = () => {
    switch (period) {
      case 'week': return 'cette semaine';
      case 'month': return 'ce mois';
      case 'quarter': return 'ce trimestre';
      case 'year': return 'cette année';
    }
  };

  return (
    <div className="space-y-6">
      {/* Vue d'ensemble */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            Tableau de Bord des Risques Financiers
          </h2>
          <div className="flex space-x-2">
            {(['current', 'optimistic', 'pessimistic'] as const).map((scenario) => (
              <button
                key={scenario}
                onClick={() => setSelectedScenario(scenario)}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                  selectedScenario === scenario
                    ? 'bg-purple-100 text-purple-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                {scenario === 'current' ? 'Actuel' :
                 scenario === 'optimistic' ? 'Optimiste' : 'Pessimiste'}
              </button>
            ))}
          </div>
        </div>

        {/* Métriques principales */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-4 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100 text-sm">Exposition totale</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(dashboardData.overview.total_risk_exposure)}
                </p>
              </div>
              <CurrencyDollarIcon className="w-8 h-8 text-red-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Régions à risque</p>
                <p className="text-2xl font-bold">{dashboardData.overview.high_risk_regions}</p>
              </div>
              <MapPinIcon className="w-8 h-8 text-orange-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Plans de mitigation</p>
                <p className="text-2xl font-bold">{dashboardData.overview.active_mitigation_plans}</p>
              </div>
              <DocumentTextIcon className="w-8 h-8 text-blue-200" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Économies potentielles</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(dashboardData.overview.potential_annual_savings)}
                </p>
              </div>
              <ArrowTrendingDownIcon className="w-8 h-8 text-green-200" />
            </div>
          </motion.div>
        </div>

        {/* Période d'analyse */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <CalendarIcon className="w-4 h-4" />
            <span>Données pour {getPeriodLabel()}</span>
            <span>•</span>
            <span>Scénario {selectedScenario === 'current' ? 'actuel' : selectedScenario === 'optimistic' ? 'optimiste' : 'pessimiste'}</span>
          </div>
        </div>
      </Card>

      {/* Distribution des risques et facteurs principaux */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Distribution des risques */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Distribution des Risques
          </h3>
          <div className="space-y-4">
            {dashboardData.risk_distribution.map((risk, index) => (
              <motion.div
                key={risk.risk_level}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-4 h-4 rounded-full ${
                    risk.risk_level === 'CRITICAL' ? 'bg-red-500' :
                    risk.risk_level === 'HIGH' ? 'bg-orange-500' :
                    risk.risk_level === 'MEDIUM' ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`} />
                  <span className="font-medium text-gray-900">
                    {risk.risk_level === 'CRITICAL' ? 'Critique' :
                     risk.risk_level === 'HIGH' ? 'Élevé' :
                     risk.risk_level === 'MEDIUM' ? 'Moyen' : 'Faible'}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600">{risk.count} région{risk.count > 1 ? 's' : ''}</span>
                  <span className="font-bold text-gray-900">{risk.percentage.toFixed(1)}%</span>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>

        {/* Facteurs de risque principaux */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Facteurs de Risque Principaux
          </h3>
          <div className="space-y-4">
            {dashboardData.top_risk_factors.map((factor, index) => (
              <motion.div
                key={factor.factor}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between"
              >
                <div>
                  <h4 className="font-medium text-gray-900">{factor.factor}</h4>
                  <p className="text-sm text-gray-600">
                    {factor.affected_regions} région{factor.affected_regions > 1 ? 's' : ''} affectée{factor.affected_regions > 1 ? 's' : ''}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">
                    {factor.impact_score.toFixed(1)}
                  </div>
                  <div className="text-xs text-gray-500">Score d'impact</div>
                </div>
              </motion.div>
            ))}
          </div>
        </Card>
      </div>

      {/* Tendances financières */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Tendances Financières
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-900">Mois</th>
                <th className="text-right py-3 px-4 font-medium text-gray-900">Pertes estimées</th>
                <th className="text-right py-3 px-4 font-medium text-gray-900">Économies mitigation</th>
                <th className="text-right py-3 px-4 font-medium text-gray-900">Impact net</th>
                <th className="text-right py-3 px-4 font-medium text-gray-900">Tendance</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {dashboardData.financial_trends.map((trend, index) => {
                const isPositive = trend.net_impact < (dashboardData.financial_trends[index - 1]?.net_impact || trend.net_impact);
                
                return (
                  <motion.tr
                    key={trend.month}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="py-3 px-4 font-medium text-gray-900">{trend.month}</td>
                    <td className="py-3 px-4 text-right text-red-600">
                      {formatCurrency(trend.estimated_losses)}
                    </td>
                    <td className="py-3 px-4 text-right text-green-600">
                      {formatCurrency(trend.mitigation_savings)}
                    </td>
                    <td className="py-3 px-4 text-right font-medium">
                      <span className={trend.net_impact > 0 ? 'text-red-600' : 'text-green-600'}>
                        {formatCurrency(Math.abs(trend.net_impact))}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-right">
                      {index > 0 && (
                        <div className="flex items-center justify-end">
                          {isPositive ? (
                            <ArrowTrendingDownIcon className="w-4 h-4 text-green-500" />
                          ) : (
                            <ArrowTrendingUpIcon className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                      )}
                    </td>
                  </motion.tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Comparaison régionale */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Comparaison Régionale
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dashboardData.regional_comparison.map((region, index) => (
            <motion.div
              key={region.region}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">{region.region}</h4>
                <div className="flex items-center space-x-1">
                  {region.trend === 'IMPROVING' ? (
                    <ArrowTrendingDownIcon className="w-4 h-4 text-green-500" />
                  ) : region.trend === 'DETERIORATING' ? (
                    <ArrowTrendingUpIcon className="w-4 h-4 text-red-500" />
                  ) : (
                    <div className="w-4 h-4 bg-gray-300 rounded-full" />
                  )}
                  <span className={`text-xs font-medium ${
                    region.trend === 'IMPROVING' ? 'text-green-600' :
                    region.trend === 'DETERIORATING' ? 'text-red-600' :
                    'text-gray-600'
                  }`}>
                    {region.trend === 'IMPROVING' ? 'Amélioration' :
                     region.trend === 'DETERIORATING' ? 'Dégradation' : 'Stable'}
                  </span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Score de risque</span>
                  <span className="font-medium">{region.risk_score.toFixed(1)}/100</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Impact financier</span>
                  <span className="font-medium text-red-600">
                    {formatCurrency(region.financial_impact)}
                  </span>
                </div>
              </div>
              
              {/* Barre de progression du risque */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      region.risk_score >= 80 ? 'bg-red-500' :
                      region.risk_score >= 60 ? 'bg-orange-500' :
                      region.risk_score >= 40 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${region.risk_score}%` }}
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Actions rapides */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions Rapides</h3>
        <div className="flex flex-wrap gap-3">
          <Button
            variant="outline"
            className="flex items-center space-x-2"
          >
            <DocumentTextIcon className="w-4 h-4" />
            <span>Générer rapport complet</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center space-x-2"
          >
            <ChartBarIcon className="w-4 h-4" />
            <span>Analyser tendances</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center space-x-2"
          >
            <ExclamationTriangleIcon className="w-4 h-4" />
            <span>Créer plan de mitigation</span>
          </Button>
        </div>
      </Card>
    </div>
  );
};
