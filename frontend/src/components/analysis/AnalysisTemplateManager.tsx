import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DocumentTextIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  PlayIcon,
  UserIcon,
  CalendarIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import analysisService from '../../services/analysis.service';
import type { AnalysisTemplate } from '../../services/analysis.service';
import { Card, Button, Loading, Modal } from '../ui';
import { formatDate, formatNumber } from '../../utils/formatters';
import toast from 'react-hot-toast';

interface AnalysisTemplateManagerProps {
  templates: AnalysisTemplate[];
  onTemplateChange: () => void;
}

export const AnalysisTemplateManager: React.FC<AnalysisTemplateManagerProps> = ({
  templates,
  onTemplateChange,
}) => {
  const queryClient = useQueryClient();
  const [selectedTemplate, setSelectedTemplate] = useState<AnalysisTemplate | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Mutation pour suppression de template
  const deleteTemplateMutation = useMutation({
    mutationFn: (templateId: string) => analysisService.deleteAnalysisTemplate(templateId),
    onSuccess: () => {
      onTemplateChange();
      toast.success('Template supprimé avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la suppression');
    }
  });

  const handleDeleteTemplate = (template: AnalysisTemplate) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le template "${template.name}" ?`)) {
      deleteTemplateMutation.mutate(template.id);
    }
  };

  const handleViewDetails = (template: AnalysisTemplate) => {
    setSelectedTemplate(template);
    setShowDetailsModal(true);
  };

  const getAnalysisTypeLabel = (type: string) => {
    switch (type) {
      case 'MINING_DETECTION':
        return 'Détection minière';
      case 'CHANGE_DETECTION':
        return 'Détection changements';
      case 'VEGETATION_ANALYSIS':
        return 'Analyse végétation';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              Gestionnaire de Templates d'Analyse
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Créez et gérez des templates réutilisables pour vos analyses
            </p>
          </div>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white"
          >
            <PlusIcon className="w-4 h-4" />
            <span>Nouveau Template</span>
          </Button>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-sm text-blue-600">Total templates</div>
            <div className="text-2xl font-bold text-blue-900">{templates.length}</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-sm text-green-600">Templates publics</div>
            <div className="text-2xl font-bold text-green-900">
              {templates.filter(t => t.is_public).length}
            </div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-sm text-purple-600">Utilisations totales</div>
            <div className="text-2xl font-bold text-purple-900">
              {templates.reduce((sum, t) => sum + t.usage_count, 0)}
            </div>
          </div>
        </div>
      </Card>

      {/* Liste des templates */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Templates disponibles ({templates.length})
          </h3>
        </div>

        {templates.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucun template
            </h3>
            <p className="text-gray-500 mb-4">
              Créez votre premier template d'analyse pour gagner du temps.
            </p>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Créer un template
            </Button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            <AnimatePresence>
              {templates.map((template, index) => (
                <motion.div
                  key={template.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  className="p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">
                          {template.name}
                        </h4>
                        {template.is_public && (
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                            Public
                          </span>
                        )}
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                          {getAnalysisTypeLabel(template.parameters.analysis_type)}
                        </span>
                      </div>
                      
                      <p className="text-gray-600 mb-3">{template.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <UserIcon className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">Par {template.created_by_name}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CalendarIcon className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">
                            {formatDate(template.created_at)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <ChartBarIcon className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">
                            {formatNumber(template.usage_count)} utilisation{template.usage_count > 1 ? 's' : ''}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">
                            {template.parameters.satellite_sources?.length || 0} source{(template.parameters.satellite_sources?.length || 0) > 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>

                      {/* Aperçu des paramètres */}
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                          <div>
                            <span className="text-gray-600">Couverture nuageuse max :</span>
                            <span className="ml-2 font-medium">
                              {template.parameters.cloud_coverage_max}%
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">Sensibilité :</span>
                            <span className="ml-2 font-medium">
                              {template.parameters.detection_sensitivity}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">Indices spectraux :</span>
                            <span className="ml-2 font-medium">
                              {template.parameters.spectral_indices?.length || 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleViewDetails(template)}
                        className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-colors"
                        title="Voir détails"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => {
                          // TODO: Implémenter l'utilisation du template
                          toast.info('Fonctionnalité à implémenter');
                        }}
                        className="p-2 text-green-600 hover:text-green-800 hover:bg-green-100 rounded-full transition-colors"
                        title="Utiliser ce template"
                      >
                        <PlayIcon className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => handleDeleteTemplate(template)}
                        disabled={deleteTemplateMutation.isPending}
                        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full transition-colors disabled:opacity-50"
                        title="Supprimer"
                      >
                        {deleteTemplateMutation.isPending ? (
                          <Loading size="sm" />
                        ) : (
                          <TrashIcon className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </Card>

      {/* Modal de détails */}
      {selectedTemplate && showDetailsModal && (
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          size="xl"
        >
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">
                Détails du Template
              </h2>
              <button
                onClick={() => setShowDetailsModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Informations générales */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Informations générales
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div>
                    <span className="text-gray-600">Nom :</span>
                    <span className="ml-2 font-medium">{selectedTemplate.name}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Description :</span>
                    <p className="mt-1 text-gray-900">{selectedTemplate.description}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600">Créé par :</span>
                      <span className="ml-2 font-medium">{selectedTemplate.created_by_name}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Date de création :</span>
                      <span className="ml-2 font-medium">
                        {formatDate(selectedTemplate.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Paramètres d'analyse */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Paramètres d'analyse
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-600">Type d'analyse :</span>
                      <span className="ml-2 font-medium">
                        {getAnalysisTypeLabel(selectedTemplate.parameters.analysis_type)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Sensibilité :</span>
                      <span className="ml-2 font-medium">
                        {selectedTemplate.parameters.detection_sensitivity}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Couverture nuageuse max :</span>
                      <span className="ml-2 font-medium">
                        {selectedTemplate.parameters.cloud_coverage_max}%
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Période :</span>
                      <span className="ml-2 font-medium">
                        {selectedTemplate.parameters.start_date} → {selectedTemplate.parameters.end_date}
                      </span>
                    </div>
                  </div>
                  
                  {selectedTemplate.parameters.satellite_sources && (
                    <div>
                      <span className="text-gray-600">Sources satellites :</span>
                      <div className="mt-1 flex flex-wrap gap-2">
                        {selectedTemplate.parameters.satellite_sources.map((source) => (
                          <span
                            key={source}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                          >
                            {source}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {selectedTemplate.parameters.spectral_indices && (
                    <div>
                      <span className="text-gray-600">Indices spectraux :</span>
                      <div className="mt-1 flex flex-wrap gap-2">
                        {selectedTemplate.parameters.spectral_indices.map((index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full"
                          >
                            {index}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Statistiques d'utilisation */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Statistiques d'utilisation
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">
                        {formatNumber(selectedTemplate.usage_count)}
                      </div>
                      <div className="text-sm text-gray-600">Utilisations</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {selectedTemplate.is_public ? 'Oui' : 'Non'}
                      </div>
                      <div className="text-sm text-gray-600">Public</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {formatDate(selectedTemplate.created_at, { month: 'short', year: 'numeric' })}
                      </div>
                      <div className="text-sm text-gray-600">Créé</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => setShowDetailsModal(false)}
              >
                Fermer
              </Button>
              <Button
                onClick={() => {
                  // TODO: Implémenter l'utilisation du template
                  toast.info('Fonctionnalité à implémenter');
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Utiliser ce template
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Modal de création (placeholder) */}
      {showCreateModal && (
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          size="lg"
        >
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              Créer un nouveau template
            </h2>
            <div className="text-center py-8">
              <DocumentTextIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">
                Fonctionnalité de création de template à implémenter
              </p>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                Fermer
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};
