import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FunnelIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  MapPinIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, <PERSON> } from '../ui';
import type {
  DetectionFilters as IDetectionFilters,
  DetectionType,
  ValidationStatus,
} from '../../types/detection.types.js';
import {
  DETECTION_TYPE_LABELS,
  VALIDATION_STATUS_LABELS,
} from '../../types/detection.types.js';

interface DetectionFiltersProps {
  filters: IDetectionFilters;
  onFiltersChange: (filters: IDetectionFilters) => void;
  onReset: () => void;
  isLoading?: boolean;
}

export const DetectionFilters: React.FC<DetectionFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset,
  isLoading = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterChange = (key: keyof IDetectionFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const hasActiveFilters = Object.values(filters).some(value =>
    value !== undefined && value !== 'all' && value !== ''
  );

  return (
    <Card className="p-4 mb-6">
      {/* Barre de recherche principale */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
          <input
            type="text"
            placeholder="Rechercher par région, type..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <Button
          variant="outline"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center space-x-2"
        >
          <AdjustmentsHorizontalIcon className="w-4 h-4" />
          <span>Filtres avancés</span>
          {hasActiveFilters && (
            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
          )}
        </Button>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            onClick={onReset}
            className="flex items-center space-x-2 text-slate-600"
          >
            <XMarkIcon className="w-4 h-4" />
            <span>Réinitialiser</span>
          </Button>
        )}
      </div>

      {/* Filtres rapides */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={() => handleFilterChange('validation_status', 'DETECTED')}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            filters.validation_status === 'DETECTED'
              ? 'bg-slate-100 text-slate-900 border border-slate-300'
              : 'bg-slate-50 text-slate-600 hover:bg-slate-100'
          }`}
        >
          🔍 Non validées
        </button>

        <button
          onClick={() => handleFilterChange('confidence_min', 0.8)}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            filters.confidence_min === 0.8
              ? 'bg-green-100 text-green-900 border border-green-300'
              : 'bg-slate-50 text-slate-600 hover:bg-slate-100'
          }`}
        >
          🎯 Haute confiance
        </button>

        <button
          onClick={() => handleFilterChange('detection_type', 'MINING_SITE')}
          className={`px-3 py-1 rounded-full text-sm transition-colors ${
            filters.detection_type === 'MINING_SITE'
              ? 'bg-orange-100 text-orange-900 border border-orange-300'
              : 'bg-slate-50 text-slate-600 hover:bg-slate-100'
          }`}
        >
          ⛏️ Sites miniers
        </button>
      </div>

      {/* Filtres avancés */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-slate-200 pt-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Type de détection */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de détection
                </label>
                <select
                  value={filters.detection_type || 'all'}
                  onChange={(e) => handleFilterChange('detection_type', e.target.value === 'all' ? undefined : e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les types</option>
                  {Object.entries(DETECTION_TYPE_LABELS).map(([key, label]) => (
                    <option key={key} value={key}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Statut de validation */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Statut de validation
                </label>
                <select
                  value={filters.validation_status || 'all'}
                  onChange={(e) => handleFilterChange('validation_status', e.target.value === 'all' ? undefined : e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tous les statuts</option>
                  {Object.entries(VALIDATION_STATUS_LABELS).map(([key, label]) => (
                    <option key={key} value={key}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Confiance minimale */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Confiance minimale
                </label>
                <select
                  value={filters.confidence_min || ''}
                  onChange={(e) => handleFilterChange('confidence_min', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Toutes</option>
                  <option value="0.8">Élevée (≥ 80%)</option>
                  <option value="0.5">Moyenne (≥ 50%)</option>
                  <option value="0.3">Faible (≥ 30%)</option>
                </select>
              </div>

              {/* Tri */}
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Trier par
                </label>
                <select
                  value={filters.ordering || '-detection_date'}
                  onChange={(e) => handleFilterChange('ordering', e.target.value)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="-detection_date">Plus récentes</option>
                  <option value="detection_date">Plus anciennes</option>
                  <option value="-confidence_score">Confiance décroissante</option>
                  <option value="confidence_score">Confiance croissante</option>
                  <option value="-area_hectares">Surface décroissante</option>
                  <option value="area_hectares">Surface croissante</option>
                </select>
              </div>
            </div>

            {/* Filtres de date */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  <CalendarIcon className="w-4 h-4 inline mr-1" />
                  Date de début
                </label>
                <input
                  type="date"
                  value={filters.date_from || ''}
                  onChange={(e) => handleFilterChange('date_from', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  <CalendarIcon className="w-4 h-4 inline mr-1" />
                  Date de fin
                </label>
                <input
                  type="date"
                  value={filters.date_to || ''}
                  onChange={(e) => handleFilterChange('date_to', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
};
