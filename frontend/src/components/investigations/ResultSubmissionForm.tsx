import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import type { Investigation } from '../../services/investigation.service.js';
import { Modal, Button, Loading } from '../ui';

interface ResultSubmissionFormProps {
  isOpen: boolean;
  onClose: () => void;
  investigation: Investigation | null;
  onSubmit: (result: string, fieldNotes: string, investigationDate?: string) => void;
  isLoading?: boolean;
}

export const ResultSubmissionForm: React.FC<ResultSubmissionFormProps> = ({
  isOpen,
  onClose,
  investigation,
  onSubmit,
  isLoading = false,
}) => {
  const [result, setResult] = useState<string>('');
  const [fieldNotes, setFieldNotes] = useState<string>('');
  const [investigationDate, setInvestigationDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (result && fieldNotes) {
      onSubmit(result, fieldNotes, investigationDate);
    }
  };

  const handleClose = () => {
    setResult('');
    setFieldNotes('');
    setInvestigationDate(new Date().toISOString().split('T')[0]);
    onClose();
  };

  if (!isOpen || !investigation) return null;

  const resultOptions = [
    { value: 'CONFIRMED', label: 'Confirmé', color: 'text-red-600', icon: ExclamationTriangleIcon },
    { value: 'FALSE_POSITIVE', label: 'Faux positif', color: 'text-green-600', icon: CheckCircleIcon },
    { value: 'NEEDS_MONITORING', label: 'Nécessite surveillance', color: 'text-orange-600', icon: ExclamationTriangleIcon },
    { value: 'INCONCLUSIVE', label: 'Non concluant', color: 'text-gray-600', icon: DocumentTextIcon },
  ];

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <DocumentTextIcon className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Soumettre Résultat - Investigation #{investigation.id}
              </h2>
              <p className="text-sm text-gray-600">
                {investigation.detection_info?.type} - {investigation.detection_info?.area_hectares?.toFixed(2)} ha
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Informations investigation */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-900 mb-2">Détails de l'investigation</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Coordonnées :</span>
              <span className="ml-2 font-medium">{investigation.target_coordinates}</span>
            </div>
            <div>
              <span className="text-gray-600">Date assignation :</span>
              <span className="ml-2 font-medium">
                {new Date(investigation.created_at).toLocaleDateString('fr-FR')}
              </span>
            </div>
          </div>
          {investigation.access_instructions && (
            <div className="mt-2">
              <span className="text-gray-600">Instructions d'accès :</span>
              <p className="mt-1 text-sm text-gray-800">{investigation.access_instructions}</p>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Date d'investigation */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="w-4 h-4 inline mr-1" />
              Date de l'investigation terrain
            </label>
            <input
              type="date"
              value={investigationDate}
              onChange={(e) => setInvestigationDate(e.target.value)}
              max={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              required
            />
          </div>

          {/* Résultat */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Résultat de l'investigation *
            </label>
            <div className="space-y-2">
              {resultOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <motion.div
                    key={option.value}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      result === option.value
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setResult(option.value)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                        result === option.value
                          ? 'border-green-500 bg-green-500'
                          : 'border-gray-300'
                      }`}>
                        {result === option.value && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <IconComponent className={`w-5 h-5 ${option.color}`} />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{option.label}</h4>
                        <p className="text-sm text-gray-600">
                          {option.value === 'CONFIRMED' && 'Site d\'orpaillage confirmé sur le terrain'}
                          {option.value === 'FALSE_POSITIVE' && 'Aucune activité d\'orpaillage détectée'}
                          {option.value === 'NEEDS_MONITORING' && 'Activité suspecte nécessitant un suivi'}
                          {option.value === 'INCONCLUSIVE' && 'Investigation non concluante (accès difficile, etc.)'}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Notes terrain */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes de terrain détaillées *
            </label>
            <textarea
              value={fieldNotes}
              onChange={(e) => setFieldNotes(e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Décrivez en détail vos observations sur le terrain :
- État du site
- Équipements observés
- Personnes présentes
- Impact environnemental
- Photos prises
- Difficultés rencontrées
- Recommandations"
              required
            />
            <div className="mt-1 flex items-center justify-between">
              <p className="text-xs text-gray-500">
                Minimum 50 caractères. Soyez précis et objectif dans vos observations.
              </p>
              <span className={`text-xs font-medium ${
                fieldNotes.length >= 50 ? 'text-green-600' : 'text-red-500'
              }`}>
                {fieldNotes.length}/50
              </span>
            </div>
          </div>

          {/* Validation */}
          {result === 'CONFIRMED' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900">Site confirmé</h4>
                  <p className="text-sm text-red-700 mt-1">
                    Ce résultat déclenchera automatiquement une alerte aux autorités compétentes.
                    Assurez-vous que vos observations sont précises et complètes.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <div className="relative">
              <Button
                type="submit"
                disabled={!result || !fieldNotes || fieldNotes.length < 50 || isLoading}
                className="flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <Loading size="sm" />
                    <span>Soumission...</span>
                  </>
                ) : (
                  <>
                    <CheckCircleIcon className="w-4 h-4" />
                    <span>Soumettre Résultat</span>
                  </>
                )}
              </Button>

              {/* Tooltip pour expliquer pourquoi le bouton est désactivé */}
              {(!result || !fieldNotes || fieldNotes.length < 50) && !isLoading && (
                <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                  {!result && "Sélectionnez un résultat"}
                  {result && !fieldNotes && "Ajoutez des notes"}
                  {result && fieldNotes && fieldNotes.length < 50 && `Ajoutez ${50 - fieldNotes.length} caractères`}
                </div>
              )}
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};
