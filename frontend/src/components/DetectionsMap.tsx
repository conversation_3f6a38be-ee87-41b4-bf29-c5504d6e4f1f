import React from 'react';

interface DetectionsMapProps {
  detections: any[];
  onDetectionClick?: (detection: any) => void;
  height?: string;
}

export const DetectionsMap: React.FC<DetectionsMapProps> = ({
  detections,
  onDetectionClick,
  height = '500px'
}) => {
  console.log('DetectionsMap - Détections reçues:', detections);

  return (
    <div
      className="flex items-center justify-center bg-slate-100 rounded-lg border border-slate-200"
      style={{ height }}
    >
      <div className="text-center">
        <div className="text-4xl mb-2">🗺️</div>
        <p className="text-slate-600">Carte des détections</p>
        <p className="text-sm text-slate-500 mt-2">
          {detections ? `${detections.length} détections trouvées` : 'Chargement...'}
        </p>
        {detections && detections.length > 0 && (
          <div className="mt-4 space-y-2">
            <p className="text-sm font-medium">Détections:</p>
            {detections.slice(0, 3).map((detection) => (
              <div key={detection.id} className="text-xs bg-white p-2 rounded">
                #{detection.id} - {detection.detection_type} - {detection.region_name}
                <br />
                📍 {detection.latitude?.toFixed(4)}, {detection.longitude?.toFixed(4)}
              </div>
            ))}
            {detections.length > 3 && (
              <p className="text-xs text-slate-500">... et {detections.length - 3} autres</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};