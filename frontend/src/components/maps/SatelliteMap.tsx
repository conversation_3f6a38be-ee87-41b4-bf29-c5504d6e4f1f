import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix pour les icônes Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

export interface SatelliteMapProps {
  center: [number, number];
  zoom: number;
  className?: string;
  showLayerControl?: boolean;
  showLabels?: boolean;
  onMapReady?: (map: L.Map) => void;
  children?: React.ReactNode;
}

export const SatelliteMap: React.FC<SatelliteMapProps> = ({
  center,
  zoom,
  className = '',
  showLayerControl = false,
  showLabels = true,
  onMapReady,
  children,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialiser la carte
    if (!mapInstanceRef.current) {
      try {
        mapInstanceRef.current = L.map(mapRef.current).setView(center, zoom);

        // Couche satellite principale (Esri World Imagery)
        const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri, Maxar, Earthstar Geographics, and the GIS User Community',
          maxZoom: 18,
        });

        // Couche de labels (optionnelle)
        const labelsLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}', {
          attribution: '© Esri',
          maxZoom: 18,
          opacity: 0.7,
        });

        // Ajouter la couche satellite
        satelliteLayer.addTo(mapInstanceRef.current);

        // Ajouter les labels si demandé
        if (showLabels) {
          labelsLayer.addTo(mapInstanceRef.current);
        }

        // Ajouter le contrôle de couches si demandé
        if (showLayerControl) {
          const baseLayers = {
            '🛰️ Satellite': satelliteLayer,
            '🗺️ OpenStreetMap': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
              attribution: '© OpenStreetMap contributors',
            }),
            '🌍 Terrain': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}', {
              attribution: '© Esri, USGS, NOAA',
            }),
          };

          const overlayLayers: { [key: string]: L.TileLayer } = {};
          if (showLabels) {
            overlayLayers['🏷️ Labels'] = labelsLayer;
          }

          L.control.layers(baseLayers, overlayLayers, {
            position: 'topright',
            collapsed: true
          }).addTo(mapInstanceRef.current);
        }

        // Callback quand la carte est prête
        if (onMapReady) {
          onMapReady(mapInstanceRef.current);
        }

        console.log('SatelliteMap: Map initialized successfully');
      } catch (error) {
        console.error('SatelliteMap: Error initializing map:', error);
      }
    }

    // Cleanup
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [center, zoom, showLayerControl, showLabels, onMapReady]);

  return (
    <div className={`relative ${className}`}>
      <div ref={mapRef} className="w-full h-full rounded-lg" />
      {children}
    </div>
  );
};

export default SatelliteMap;
