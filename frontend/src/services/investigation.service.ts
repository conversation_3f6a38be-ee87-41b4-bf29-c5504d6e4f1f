import axiosInstance from './axios.config';

export interface Investigation {
  id: number;
  detection: number;
  detection_info: {
    id: number;
    type: string;
    confidence_score: number;
    area_hectares: number;
    detection_date: string;
  };
  target_coordinates: string;
  access_instructions: string;
  assigned_to: number | null;
  assigned_to_name: string | null;
  status: string;
  status_display: string;
  result: string | null;
  result_display: string | null;
  field_notes: string;
  investigation_date: string | null;
  created_at: string;
  updated_at: string;
  agents: Array<{
    id: number;
    name: string;
  }>;
  region: string;
}

export interface Agent {
  id: number;
  full_name: string;
  identifier: string;
  email: string;
  active_investigations_count: number;
  pending_investigations_count: number;
  total_workload: number;
  availability_status: string;
  last_login: string | null;
}

export interface InvestigationFilters {
  status?: string;
  agent?: string;
  region?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

class InvestigationService {
  async getInvestigations(params?: any): Promise<{ count: number; results: Investigation[] }> {
    try {
      const response = await axiosInstance.get('/investigations/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération investigations:', error);
      throw new Error('Impossible de récupérer les investigations');
    }
  }

  async getInvestigation(id: number): Promise<Investigation> {
    try {
      const response = await axiosInstance.get(`/investigations/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération investigation:', error);
      throw new Error('Impossible de récupérer l\'investigation');
    }
  }

  async createInvestigation(data: {
    detection_id: number;
    access_instructions?: string;
    assigned_to?: number;
  }): Promise<{
    success: boolean;
    message: string;
    data: Investigation;
  }> {
    try {
      const response = await axiosInstance.post('/investigations/', data);
      return response.data;
    } catch (error: any) {
      console.error('Erreur création investigation:', error);
      const errorMessage = error.response?.data?.error || 'Impossible de créer l\'investigation';
      throw new Error(errorMessage);
    }
  }

  async getPendingInvestigations(): Promise<{ count: number; results: Investigation[] }> {
    try {
      const response = await axiosInstance.get('/investigations/pending/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération investigations en attente:', error);
      throw new Error('Impossible de récupérer les investigations en attente');
    }
  }

  async getMyInvestigations(): Promise<{ count: number; results: Investigation[] }> {
    try {
      const response = await axiosInstance.get('/investigations/assigned-to-me/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération mes investigations:', error);
      throw new Error('Impossible de récupérer vos investigations');
    }
  }

  async getAvailableAgents(): Promise<{
    count: number;
    agents: Agent[];
    summary: {
      total_agents: number;
      available_agents: number;
      busy_agents: number;
      overloaded_agents: number;
    };
  }> {
    try {
      const response = await axiosInstance.get('/investigations/available-agents/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération agents disponibles:', error);
      throw new Error('Impossible de récupérer les agents disponibles');
    }
  }

  async assignInvestigation(
    id: number,
    assignedTo: number,
    priority: string = 'MEDIUM',
    notes: string = ''
  ): Promise<{
    success: boolean;
    message: string;
    data: Investigation;
    agent_info: {
      name: string;
      new_workload: number;
    };
    warning?: string;
  }> {
    try {
      const response = await axiosInstance.patch(`/investigations/${id}/assign/`, {
        assigned_to: assignedTo,
        priority,
        notes,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur assignation investigation:', error);
      throw new Error('Impossible d\'assigner l\'investigation');
    }
  }

  async submitResult(
    id: number,
    result: string,
    fieldNotes: string,
    investigationDate?: string
  ): Promise<{
    message: string;
    data: Investigation;
  }> {
    try {
      const response = await axiosInstance.patch(`/investigations/${id}/result/`, {
        result,
        field_notes: fieldNotes,
        investigation_date: investigationDate,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur soumission résultat:', error);
      throw new Error('Impossible de soumettre le résultat');
    }
  }

  // Nouvelles méthodes pour le filtrage et la pagination

  async getFilteredInvestigations(
    filters: InvestigationFilters,
    page: number = 0,
    pageSize: number = 20
  ): Promise<{ count: number; results: Investigation[] }> {
    try {
      const params = {
        page: page + 1, // L'API utilise une pagination 1-based
        page_size: pageSize,
        ...filters,
      };

      const response = await axiosInstance.get('/investigations/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération investigations filtrées:', error);
      throw new Error('Impossible de récupérer les investigations filtrées');
    }
  }

  async getInvestigationRegions(): Promise<string[]> {
    try {
      const response = await axiosInstance.get('/investigations/');
      const investigations = response.data.results;
      return [...new Set(investigations.map((i: Investigation) => i.region))];
    } catch (error) {
      console.error('Erreur récupération régions investigations:', error);
      return [];
    }
  }

  async getInvestigationAgents(): Promise<Array<{ id: number; name: string }>> {
    try {
      const response = await axiosInstance.get('/investigations/');
      const investigations = response.data.results;
      const agents = investigations.flatMap((i: Investigation) => i.agents);
      return [...new Map(agents.map(agent => [agent.id, agent])).values()];
    } catch (error) {
      console.error('Erreur récupération agents investigations:', error);
      return [];
    }
  }
}

export default new InvestigationService();