import axiosInstance from './axios.config';

// Types pour l'évaluation des risques financiers
export interface FinancialRisk {
  id: number;
  detection: number;
  detection_info: {
    id: number;
    type: string;
    confidence_score: number;
    coordinates: string;
  };
  area_hectares: number;
  cost_per_hectare: number;
  estimated_loss: number;
  sensitive_zone_distance_km: number;
  occurrence_count: number;
  risk_level: string;
  created_at: string;
}

export interface FinancialRiskProfile {
  id: string;
  region_id: number;
  region_name: string;
  total_risk_score: number;
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  last_assessment_date: string;
  factors: {
    mining_activity_risk: number;
    environmental_degradation: number;
    legal_compliance_risk: number;
    economic_impact: number;
    social_impact: number;
  };
  financial_metrics: {
    estimated_annual_loss: number;
    potential_fines: number;
    remediation_costs: number;
    lost_revenue: number;
    insurance_claims: number;
  };
  trend: 'IMPROVING' | 'STABLE' | 'DETERIORATING';
  recommendations: string[];
  next_review_date: string;
}

export interface RiskFactorConfig {
  id: string;
  name: string;
  description: string;
  weight: number;
  calculation_method: 'AUTOMATIC' | 'MANUAL' | 'HYBRID';
  data_sources: string[];
  update_frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  is_active: boolean;
}

export interface FinancialImpactCalculation {
  scenario: 'BEST_CASE' | 'MOST_LIKELY' | 'WORST_CASE';
  timeframe_months: number;
  assumptions: {
    gold_price_per_gram: number;
    environmental_multiplier: number;
    legal_penalty_rate: number;
    remediation_cost_per_hectare: number;
    productivity_loss_percentage: number;
  };
  results: {
    total_estimated_impact: number;
    breakdown: {
      direct_losses: number;
      indirect_losses: number;
      regulatory_costs: number;
      opportunity_costs: number;
    };
    confidence_interval: {
      lower_bound: number;
      upper_bound: number;
    };
  };
}

export interface RiskMitigationPlan {
  id: string;
  risk_profile_id: string;
  title: string;
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  estimated_cost: number;
  estimated_savings: number;
  roi_percentage: number;
  implementation_timeline: string;
  responsible_department: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  milestones: Array<{
    title: string;
    due_date: string;
    status: 'PENDING' | 'COMPLETED';
    cost: number;
  }>;
  created_at: string;
  updated_at: string;
}

export interface RiskDashboardData {
  overview: {
    total_risk_exposure: number;
    high_risk_regions: number;
    active_mitigation_plans: number;
    potential_annual_savings: number;
  };
  risk_distribution: Array<{
    risk_level: string;
    count: number;
    percentage: number;
  }>;
  top_risk_factors: Array<{
    factor: string;
    impact_score: number;
    affected_regions: number;
  }>;
  financial_trends: Array<{
    month: string;
    estimated_losses: number;
    mitigation_savings: number;
    net_impact: number;
  }>;
  regional_comparison: Array<{
    region: string;
    risk_score: number;
    financial_impact: number;
    trend: string;
  }>;
}

class FinancialRiskService {
  // Méthodes existantes (compatibilité)
  async getFinancialRisks(params?: any): Promise<{ count: number; results: FinancialRisk[] }> {
    try {
      const response = await axiosInstance.get('/financial-risks/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération risques financiers:', error);
      throw new Error('Impossible de récupérer les risques financiers');
    }
  }

  async getFinancialRisk(id: number): Promise<FinancialRisk> {
    try {
      const response = await axiosInstance.get(`/financial-risks/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération risque financier:', error);
      throw new Error('Impossible de récupérer le risque financier');
    }
  }

  async getHighImpactRisks(): Promise<{
    count: number;
    total_estimated_loss: number;
    results: FinancialRisk[];
  }> {
    try {
      const response = await axiosInstance.get('/financial-risks/high-impact/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération risques à fort impact:', error);
      throw new Error('Impossible de récupérer les risques à fort impact');
    }
  }

  // ========== NOUVELLES MÉTHODES SPRINT 6 ==========

  // Récupération des profils de risque par région
  async getRiskProfiles(): Promise<FinancialRiskProfile[]> {
    try {
      const response = await axiosInstance.get('/financial-risk/profiles/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération profils de risque:', error);
      throw new Error('Impossible de récupérer les profils de risque');
    }
  }

  // Récupération d'un profil de risque spécifique
  async getRiskProfile(profileId: string): Promise<FinancialRiskProfile> {
    try {
      const response = await axiosInstance.get(`/financial-risk/profiles/${profileId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération profil de risque:', error);
      throw new Error('Impossible de récupérer le profil de risque');
    }
  }

  // Calcul d'impact financier avec scénarios
  async calculateFinancialImpact(
    regionId: number,
    scenario: 'BEST_CASE' | 'MOST_LIKELY' | 'WORST_CASE',
    timeframeMonths: number = 12,
    customAssumptions?: Partial<FinancialImpactCalculation['assumptions']>
  ): Promise<FinancialImpactCalculation> {
    try {
      // Endpoint non disponible - utiliser les données existantes et calculer
      console.warn('Endpoint calculate-impact non disponible, utilisation de calcul simulé');

      // Récupérer les risques existants pour la région
      const risksResponse = await axiosInstance.get('/financial-risks/', {
        params: { region: regionId }
      });

      const risks = risksResponse.data.results || [];
      const totalLoss = risks.reduce((sum: number, risk: any) => sum + (risk.estimated_loss || 0), 0);

      // Facteurs de scénario
      const scenarioFactors = {
        'BEST_CASE': 0.5,
        'MOST_LIKELY': 1.0,
        'WORST_CASE': 2.0
      };

      const factor = scenarioFactors[scenario];
      const projectedLoss = totalLoss * factor * (timeframeMonths / 12);

      return {
        scenario,
        timeframe_months: timeframeMonths,
        region_id: regionId,
        total_estimated_loss: projectedLoss,
        confidence_level: 0.75,
        assumptions: {
          deforestation_rate_per_month: 2.5,
          cost_per_hectare: 50000,
          detection_accuracy: 0.85,
          enforcement_effectiveness: 0.6,
          ...customAssumptions
        },
        breakdown: {
          direct_environmental_damage: projectedLoss * 0.6,
          lost_ecosystem_services: projectedLoss * 0.25,
          enforcement_costs: projectedLoss * 0.1,
          rehabilitation_costs: projectedLoss * 0.05
        },
        mitigation_options: [
          {
            name: 'Surveillance renforcée',
            cost: projectedLoss * 0.1,
            effectiveness: 0.7,
            potential_savings: projectedLoss * 0.5
          }
        ]
      };
    } catch (error) {
      console.error('Erreur calcul impact financier:', error);
      throw new Error('Impossible de calculer l\'impact financier');
    }
  }

  // Gestion des facteurs de risque
  async getRiskFactors(): Promise<RiskFactorConfig[]> {
    try {
      const response = await axiosInstance.get('/financial-risk/factors/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération facteurs de risque:', error);
      throw new Error('Impossible de récupérer les facteurs de risque');
    }
  }

  async updateRiskFactor(factorId: string, updates: Partial<RiskFactorConfig>): Promise<RiskFactorConfig> {
    try {
      const response = await axiosInstance.patch(`/financial-risk/factors/${factorId}/`, updates);
      return response.data;
    } catch (error) {
      console.error('Erreur mise à jour facteur de risque:', error);
      throw new Error('Impossible de mettre à jour le facteur de risque');
    }
  }

  // Gestion des plans de mitigation
  async getMitigationPlans(riskProfileId?: string): Promise<RiskMitigationPlan[]> {
    try {
      const params = riskProfileId ? { risk_profile_id: riskProfileId } : {};
      const response = await axiosInstance.get('/financial-risk/mitigation-plans/', { params });
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération plans de mitigation:', error);
      throw new Error('Impossible de récupérer les plans de mitigation');
    }
  }

  async createMitigationPlan(plan: Omit<RiskMitigationPlan, 'id' | 'created_at' | 'updated_at'>): Promise<RiskMitigationPlan> {
    try {
      const response = await axiosInstance.post('/financial-risk/mitigation-plans/', plan);
      return response.data;
    } catch (error) {
      console.error('Erreur création plan de mitigation:', error);
      throw new Error('Impossible de créer le plan de mitigation');
    }
  }

  async updateMitigationPlan(planId: string, updates: Partial<RiskMitigationPlan>): Promise<RiskMitigationPlan> {
    try {
      const response = await axiosInstance.patch(`/financial-risk/mitigation-plans/${planId}/`, updates);
      return response.data;
    } catch (error) {
      console.error('Erreur mise à jour plan de mitigation:', error);
      throw new Error('Impossible de mettre à jour le plan de mitigation');
    }
  }

  async deleteMitigationPlan(planId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.delete(`/financial-risk/mitigation-plans/${planId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur suppression plan de mitigation:', error);
      throw new Error('Impossible de supprimer le plan de mitigation');
    }
  }

  // Données du tableau de bord des risques
  async getRiskDashboardData(period?: 'week' | 'month' | 'quarter' | 'year'): Promise<RiskDashboardData> {
    try {
      // Utiliser l'endpoint stats existant pour l'impact financier
      const response = await axiosInstance.get('/stats/financial-impact/');
      const financialData = response.data;

      // Transformer les données pour correspondre à l'interface RiskDashboardData
      const dashboardData: RiskDashboardData = {
        overview: {
          total_risk_exposure: financialData.total_financial_impact || 0,
          high_risk_regions: financialData.high_risk_regions || 0,
          active_mitigation_plans: 0, // Non disponible dans les stats
          potential_annual_savings: 0, // Non disponible dans les stats
        },
        risk_distribution: financialData.risk_breakdown || [],
        top_risk_factors: [], // Non disponible dans les stats
        financial_trends: [], // Non disponible dans les stats
        regional_comparison: financialData.regional_breakdown || [],
      };

      return dashboardData;
    } catch (error) {
      console.error('Erreur récupération données tableau de bord:', error);
      throw new Error('Impossible de récupérer les données du tableau de bord');
    }
  }

  // Génération de rapport de risque complet
  async generateRiskReport(
    regionIds?: number[],
    includeProjections: boolean = true,
    includeMitigationPlans: boolean = true
  ): Promise<{
    report_id: string;
    download_url: string;
    expires_at: string;
  }> {
    try {
      // Endpoint non disponible - utiliser une approche alternative
      // Pour l'instant, retourner des données simulées
      console.warn('Endpoint generate-report non disponible, utilisation de données simulées');

      return {
        report_id: `report_${Date.now()}`,
        download_url: '#', // URL simulée
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24h
      };
    } catch (error) {
      console.error('Erreur génération rapport de risque:', error);
      throw new Error('Impossible de générer le rapport de risque');
    }
  }

  // Simulation de scénarios de risque
  async simulateRiskScenario(
    regionId: number,
    scenarioChanges: {
      mining_activity_change?: number;
      environmental_protection_level?: number;
      enforcement_strength?: number;
      economic_conditions?: number;
    }
  ): Promise<{
    original_risk_score: number;
    simulated_risk_score: number;
    risk_change_percentage: number;
    financial_impact_change: number;
    recommendations: string[];
  }> {
    try {
      // Endpoint non disponible - utiliser une approche alternative
      console.warn('Endpoint simulate-scenario non disponible, utilisation de données simulées');

      // Simulation basique basée sur les changements
      const baseRiskScore = 75; // Score de base simulé
      const totalChange = Object.values(scenarioChanges).reduce((sum, val) => sum + (val || 0), 0);
      const simulatedScore = Math.max(0, Math.min(100, baseRiskScore + totalChange));

      return {
        original_risk_score: baseRiskScore,
        simulated_risk_score: simulatedScore,
        risk_change_percentage: ((simulatedScore - baseRiskScore) / baseRiskScore) * 100,
        financial_impact_change: (simulatedScore - baseRiskScore) * 100000, // Impact simulé
        recommendations: [
          'Renforcer la surveillance dans la région',
          'Améliorer les mesures de protection environnementale',
          'Augmenter les patrouilles de contrôle'
        ],
      };
    } catch (error) {
      console.error('Erreur simulation scénario de risque:', error);
      throw new Error('Impossible de simuler le scénario de risque');
    }
  }

  // Alertes de risque automatiques
  async getRiskAlerts(): Promise<Array<{
    id: string;
    region: string;
    risk_type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    message: string;
    financial_impact: number;
    created_at: string;
    is_acknowledged: boolean;
  }>> {
    try {
      // Utiliser l'endpoint alerts existant et filtrer par type financier
      const response = await axiosInstance.get('/alerts/', {
        params: { alert_type: 'FINANCIAL_RISK' }
      });

      // Transformer les alertes pour correspondre à l'interface attendue
      const alerts = (response.data.results || response.data).map((alert: any) => ({
        id: alert.id.toString(),
        region: alert.region || 'Région inconnue',
        risk_type: 'FINANCIAL_RISK',
        severity: alert.level || 'MEDIUM',
        message: alert.message || 'Alerte de risque financier',
        financial_impact: 0, // Non disponible dans les alertes standard
        created_at: alert.created_at,
        is_acknowledged: alert.alert_status === 'ACKNOWLEDGED',
      }));

      return alerts;
    } catch (error) {
      console.error('Erreur récupération alertes de risque:', error);
      throw new Error('Impossible de récupérer les alertes de risque');
    }
  }

  async acknowledgeRiskAlert(alertId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Utiliser l'endpoint alerts existant pour l'accusé de réception
      const response = await axiosInstance.patch(`/alerts/${alertId}/acknowledge/`);
      return response.data;
    } catch (error) {
      console.error('Erreur accusé réception alerte de risque:', error);
      throw new Error('Impossible d\'accuser réception de l\'alerte');
    }
  }
}

export default new FinancialRiskService();