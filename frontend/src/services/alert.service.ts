import axiosInstance from './axios.config';

// Types pour les alertes selon spécifications DoD
export interface Alert {
  id: number;
  name: string;
  detection: number;
  detection_info: {
    id: number;
    type: string;
    confidence_score: number;
    coordinates: string;
    area_hectares: number;
    latitude: number;
    longitude: number;
  };
  region: number;
  region_name: string;
  level: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  alert_type: 'MINING_DETECTION' | 'ENVIRONMENTAL_DAMAGE' | 'SECURITY_BREACH' | 'SYSTEM_ERROR';
  message: string;
  alert_status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED';
  sent_at: string;
  acknowledged_at?: string;
  resolved_at?: string;
  is_read: boolean;
  assigned_to: number | null;
  assigned_to_name: string | null;
  acknowledged_by?: number;
  acknowledged_by_name?: string;
  resolved_by?: number;
  resolved_by_name?: string;
  time_since_created: string;
  priority_score: number;
  requires_immediate_action: boolean;
  related_investigation_id?: number;
}

export interface AlertFilters {
  level?: string;
  alert_type?: string;
  alert_status?: string;
  region?: string;
  assigned_to?: string;
  date_from?: string;
  date_to?: string;
  is_read?: boolean;
}

export interface AlertStats {
  total_alerts: number;
  active_alerts: number;
  critical_alerts: number;
  unread_alerts: number;
  alerts_by_level: {
    CRITICAL: number;
    HIGH: number;
    MEDIUM: number;
    LOW: number;
  };
  alerts_by_status: {
    ACTIVE: number;
    ACKNOWLEDGED: number;
    RESOLVED: number;
    DISMISSED: number;
  };
  alerts_by_region: Array<{
    region: string;
    count: number;
  }>;
  response_time_avg_hours: number;
  resolution_rate_percent: number;
}

class AlertService {
  // Récupération des alertes avec filtres
  async getAlerts(filters?: AlertFilters, page: number = 1, pageSize: number = 20): Promise<{
    count: number;
    results: Alert[];
  }> {
    try {
      const params = {
        page,
        page_size: pageSize,
        ...filters,
      };
      const response = await axiosInstance.get('/alerts/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération alertes:', error);
      throw new Error('Impossible de récupérer les alertes');
    }
  }

  // Récupération d'une alerte spécifique
  async getAlert(id: number): Promise<Alert> {
    try {
      const response = await axiosInstance.get(`/alerts/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération alerte:', error);
      throw new Error('Impossible de récupérer l\'alerte');
    }
  }

  // Récupération des alertes actives
  async getActiveAlerts(): Promise<{ count: number; results: Alert[] }> {
    try {
      const response = await axiosInstance.get('/alerts/active/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération alertes actives:', error);
      throw new Error('Impossible de récupérer les alertes actives');
    }
  }

  // Récupération des alertes critiques
  async getCriticalAlerts(): Promise<{ count: number; results: Alert[] }> {
    try {
      const response = await axiosInstance.get('/alerts/critical/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération alertes critiques:', error);
      throw new Error('Impossible de récupérer les alertes critiques');
    }
  }

  // Récupération des alertes non lues
  async getUnreadAlerts(): Promise<{ count: number; results: Alert[] }> {
    try {
      const response = await axiosInstance.get('/alerts/unread/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération alertes non lues:', error);
      throw new Error('Impossible de récupérer les alertes non lues');
    }
  }

  // Récupération des alertes assignées à l'utilisateur connecté
  async getMyAlerts(): Promise<{ count: number; results: Alert[] }> {
    try {
      const response = await axiosInstance.get('/alerts/assigned-to-me/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération mes alertes:', error);
      throw new Error('Impossible de récupérer vos alertes');
    }
  }

  // Statistiques des alertes
  // DEPRECATED: Utiliser statsService.getDashboardStats() à la place
  async getAlertStats(): Promise<AlertStats> {
    try {
      // Redirection vers l'endpoint stats existant
      const response = await axiosInstance.get('/stats/dashboard/');
      const dashboardData = response.data;

      // Transformation des données pour correspondre à l'interface AlertStats
      return {
        total_alerts: dashboardData.active_alerts || 0, // Approximation
        active_alerts: dashboardData.active_alerts || 0,
        critical_alerts: dashboardData.alerts_by_level?.CRITICAL || 0,
        unread_alerts: dashboardData.active_alerts || 0, // Approximation
        alerts_by_level: {
          CRITICAL: dashboardData.alerts_by_level?.CRITICAL || 0,
          HIGH: dashboardData.alerts_by_level?.HIGH || 0,
          MEDIUM: dashboardData.alerts_by_level?.MEDIUM || 0,
          LOW: dashboardData.alerts_by_level?.LOW || 0,
        },
        alerts_by_status: {
          ACTIVE: dashboardData.active_alerts || 0,
          ACKNOWLEDGED: 0, // Non disponible dans dashboard stats
          RESOLVED: 0, // Non disponible dans dashboard stats
          DISMISSED: 0, // Non disponible dans dashboard stats
        },
        alerts_by_region: dashboardData.affected_zones?.map((zone: any) => ({
          region: zone.zone,
          count: zone.detections // Approximation
        })) || [],
        response_time_avg_hours: 0, // Non disponible dans dashboard stats
        resolution_rate_percent: 0, // Non disponible dans dashboard stats
      };
    } catch (error) {
      console.error('Erreur récupération statistiques alertes:', error);
      throw new Error('Impossible de récupérer les statistiques');
    }
  }

  // Mise à jour du statut d'une alerte
  async updateAlertStatus(
    id: number,
    status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED',
    assignedTo?: number,
    notes?: string
  ): Promise<{
    success: boolean;
    message: string;
    data: Alert;
  }> {
    try {
      const response = await axiosInstance.patch(`/alerts/${id}/status/`, {
        alert_status: status,
        assigned_to: assignedTo,
        notes,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur mise à jour statut alerte:', error);
      throw new Error('Impossible de mettre à jour le statut de l\'alerte');
    }
  }

  // Accusé de réception d'une alerte
  async acknowledgeAlert(id: number, notes?: string): Promise<{
    success: boolean;
    message: string;
    data: Alert;
  }> {
    try {
      const response = await axiosInstance.patch(`/alerts/${id}/acknowledge/`, {
        notes,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur accusé réception alerte:', error);
      throw new Error('Impossible d\'accuser réception de l\'alerte');
    }
  }

  // Résolution d'une alerte
  async resolveAlert(id: number, resolution_notes: string): Promise<{
    success: boolean;
    message: string;
    data: Alert;
  }> {
    try {
      const response = await axiosInstance.patch(`/alerts/${id}/resolve/`, {
        resolution_notes,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur résolution alerte:', error);
      throw new Error('Impossible de résoudre l\'alerte');
    }
  }

  // Marquer comme lu/non lu
  async markAsRead(id: number, isRead: boolean = true): Promise<{
    success: boolean;
    message: string;
    data: Alert;
  }> {
    try {
      const response = await axiosInstance.patch(`/alerts/${id}/read/`, {
        is_read: isRead,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur marquage lecture alerte:', error);
      throw new Error('Impossible de marquer l\'alerte comme lue');
    }
  }

  // Assignation d'une alerte
  async assignAlert(id: number, assignedTo: number, notes?: string): Promise<{
    success: boolean;
    message: string;
    data: Alert;
    agent_info: {
      name: string;
      current_alerts: number;
    };
  }> {
    try {
      const response = await axiosInstance.patch(`/alerts/${id}/assign/`, {
        assigned_to: assignedTo,
        notes,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur assignation alerte:', error);
      throw new Error('Impossible d\'assigner l\'alerte');
    }
  }

  // Création d'investigation depuis alerte
  async createInvestigationFromAlert(id: number, priority: string = 'MEDIUM', notes?: string): Promise<{
    success: boolean;
    message: string;
    investigation_id: number;
    alert: Alert;
  }> {
    try {
      const response = await axiosInstance.post(`/alerts/${id}/create-investigation/`, {
        priority,
        notes,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur création investigation depuis alerte:', error);
      throw new Error('Impossible de créer l\'investigation');
    }
  }

  // Alertes par région pour la carte
  async getAlertsByRegion(): Promise<Array<{
    region: string;
    coordinates: [number, number];
    alerts: Alert[];
    count_by_level: {
      CRITICAL: number;
      HIGH: number;
      MEDIUM: number;
      LOW: number;
    };
  }>> {
    try {
      // Essayer d'abord l'endpoint spécifique
      const response = await axiosInstance.get('/alerts/by-region/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération alertes par région:', error);

      // Fallback: récupérer toutes les alertes et les grouper par région
      try {
        const alertsResponse = await axiosInstance.get('/alerts/');
        const alerts = alertsResponse.data.results || [];

        // Grouper par région (approximation)
        const regionMap = new Map();
        alerts.forEach((alert: Alert) => {
          const regionName = alert.region || 'Région inconnue';
          if (!regionMap.has(regionName)) {
            regionMap.set(regionName, {
              region: regionName,
              coordinates: [8.0, -5.0] as [number, number], // Coordonnées par défaut Côte d'Ivoire
              alerts: [],
              count_by_level: { CRITICAL: 0, HIGH: 0, MEDIUM: 0, LOW: 0 }
            });
          }

          const regionData = regionMap.get(regionName);
          regionData.alerts.push(alert);
          regionData.count_by_level[alert.level as keyof typeof regionData.count_by_level]++;
        });

        return Array.from(regionMap.values());
      } catch (fallbackError) {
        console.error('Erreur fallback alertes par région:', fallbackError);
        throw new Error('Impossible de récupérer les alertes par région');
      }
    }
  }

  // Notifications temps réel (WebSocket ou polling)
  async getRecentAlerts(since?: string): Promise<{ count: number; results: Alert[] }> {
    try {
      const params = since ? { since } : {};
      const response = await axiosInstance.get('/alerts/recent/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération alertes récentes:', error);
      throw new Error('Impossible de récupérer les alertes récentes');
    }
  }

  // Récupération des agents avec leur charge de travail
  async getAgentsWorkload(): Promise<{ count: number; results: Array<{
    id: number;
    name: string;
    email: string;
    current_alerts: number;
    resolved_alerts: number;
    total_alerts: number;
    workload_status: 'LOW' | 'MEDIUM' | 'HIGH';
  }> }> {
    try {
      const response = await axiosInstance.get('/alerts/agents/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération agents:', error);
      throw new Error('Impossible de récupérer la liste des agents');
    }
  }
}

export default new AlertService();