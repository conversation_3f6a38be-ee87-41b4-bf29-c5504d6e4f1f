import axiosInstance from './axios.config';

// Types alignés avec le backend réel
export interface AnalysisRequest {
  months_back?: number; // Paramètre principal backend (défaut: 3)
}

export interface AnalysisResult {
  success: boolean;
  message: string;
  data: {
    images_processed: number;
    detections_found: number;
    alerts_generated: number;
    investigations_created: number;
    financial_risks_calculated: number;
    analysis_date: string;
    created_objects: {
      detections: any[];
      alerts: any[];
      financial_risks: any[];
      investigations: any[];
    };
  };
}

class AnalysisService {
  /**
   * Lance une analyse complète d'orpaillage (endpoint principal backend)
   * POST /api/v1/analysis/run/
   */
  async runAnalysis(request: AnalysisRequest = {}): Promise<AnalysisResult> {
    try {
      const response = await axiosInstance.post('/analysis/run/', {
        months_back: request.months_back || 3
      });
      return response.data;
    } catch (error: any) {
      console.error('Erreur lancement analyse:', error);
      if (error.response?.status === 400) {
        throw new Error(error.response.data.error || 'Paramètres invalides');
      }
      throw new Error('Impossible de lancer l\'analyse');
    }
  }

  /**
   * Validation simple des paramètres d'analyse
   */
  validateAnalysisRequest(request: AnalysisRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (request.months_back && (request.months_back < 1 || request.months_back > 12)) {
      errors.push('months_back doit être entre 1 et 12');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export default new AnalysisService();