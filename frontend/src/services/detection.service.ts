import axiosInstance from './axios.config';
import type {
  Detection,
  DetectionResponse,
  DetectionStats,
  ValidationResponse,
  DetectionType,
  ValidationStatus
} from '../types/detection.types';

class DetectionService {
  async getDetections(params?: any): Promise<DetectionResponse> {
    try {
      // Paramètres par défaut
      const defaultParams = {
        ordering: '-detection_date',
        ...params
      };
      const response = await axiosInstance.get('/detections/', { params: defaultParams });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections:', error);
      throw new Error('Impossible de récupérer les détections');
    }
  }

  async getDetection(id: number): Promise<Detection> {
    try {
      const response = await axiosInstance.get(`/detections/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détection:', error);
      throw new Error('Impossible de récupérer la détection');
    }
  }

  async getHighConfidenceDetections(): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/high-confidence/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections haute confiance:', error);
      throw new Error('Impossible de récupérer les détections haute confiance');
    }
  }

  async validateDetection(id: number, validationStatus: ValidationStatus, comment?: string): Promise<ValidationResponse> {
    try {
      const response = await axiosInstance.patch(`/detections/${id}/validate/`, {
        validation_status: validationStatus,
        comment: comment
      });
      return response.data;
    } catch (error) {
      console.error('Erreur validation détection:', error);
      throw new Error('Impossible de valider la détection');
    }
  }

  async deleteDetection(id: number): Promise<void> {
    try {
      await axiosInstance.delete(`/detections/${id}/`);
    } catch (error) {
      console.error('Erreur suppression détection:', error);
      throw new Error('Impossible de supprimer la détection');
    }
  }

  // Nouvelles méthodes pour les filtres avancés
  async getDetectionsByRegion(regionId: number): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { region: regionId },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par région:', error);
      throw new Error('Impossible de récupérer les détections par région');
    }
  }

  async getDetectionsByType(detectionType: DetectionType): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { detection_type: detectionType },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par type:', error);
      throw new Error('Impossible de récupérer les détections par type');
    }
  }

  async getDetectionsByStatus(validationStatus: ValidationStatus): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { validation_status: validationStatus },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par statut:', error);
      throw new Error('Impossible de récupérer les détections par statut');
    }
  }

  async getDetectionsByMultipleStatus(statuses: ValidationStatus[], params?: any): Promise<DetectionResponse> {
    try {
      // Faire plusieurs appels et combiner les résultats
      const promises = statuses.map(status =>
        axiosInstance.get('/detections/', {
          params: { ...params, validation_status: status },
        })
      );

      const responses = await Promise.all(promises);

      // Combiner tous les résultats
      const allDetections = responses.flatMap(response => response.data.results || []);
      const totalCount = responses.reduce((sum, response) => sum + (response.data.count || 0), 0);

      // Trier par date de détection (plus récent en premier)
      allDetections.sort((a, b) => new Date(b.detection_date).getTime() - new Date(a.detection_date).getTime());

      // Limiter si nécessaire
      const limit = params?.limit || 50;
      const limitedDetections = allDetections.slice(0, limit);

      return {
        count: totalCount,
        results: limitedDetections,
        next: null,
        previous: null
      };
    } catch (error) {
      console.error('Erreur récupération détections par statuts multiples:', error);
      throw new Error('Impossible de récupérer les détections par statuts multiples');
    }
  }

  // Méthodes pour les statistiques
  // DEPRECATED: Utiliser statsService.getDashboardStats() à la place
  async getDetectionStats(): Promise<DetectionStats> {
    try {
      // Redirection vers l'endpoint stats existant
      const response = await axiosInstance.get('/stats/dashboard/');

      // Transformation des données pour correspondre à l'interface DetectionStats
      const dashboardData = response.data;
      return {
        total_detections: dashboardData.total_detections,
        validated_detections: dashboardData.total_detections, // Approximation
        pending_detections: dashboardData.total_detections, // Approximation
        false_positives: 0, // Non disponible dans dashboard stats
        confidence_distribution: [], // Non disponible dans dashboard stats
        detection_types: [], // Non disponible dans dashboard stats
        monthly_trends: dashboardData.detections_trend || [],
        accuracy_rate: dashboardData.accuracy_rate,
        high_confidence_count: dashboardData.high_confidence_detections
      };
    } catch (error) {
      console.error('Erreur récupération stats détections:', error);
      throw new Error('Impossible de récupérer les statistiques de détection');
    }
  }

  async downloadReport(id: number): Promise<void> {
    try {
      const response = await axiosInstance.get(`/detections/${id}/report/`, {
        responseType: 'blob', // Important pour télécharger un fichier
      });

      // Créer un lien de téléchargement
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Extraire le nom du fichier depuis les headers ou utiliser un nom par défaut
      const contentDisposition = response.headers['content-disposition'];
      let filename = `rapport_detection_${id}.pdf`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Erreur téléchargement rapport:', error);
      const errorMessage = error.response?.data?.error || 'Impossible de télécharger le rapport';
      throw new Error(errorMessage);
    }
  }
}

export default new DetectionService();