import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  MapIcon,
  ListBulletIcon,
  ChartBarIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { usePermissions } from '../hooks/usePermissions';
import { DetectionFilters } from '../components/detections/DetectionFilters';
import { DetectionCard } from '../components/detections/DetectionCard';
import { DetectionMap } from '../components/detections/DetectionMap';
import { Card, Button, Loading, StatCard } from '../components/ui';
import detectionService from '../services/detection.service';
import statsService from '../services/stats.service';
import type { Detection, DetectionFilters as IDetectionFilters } from '../types/detection.types.js';
import toast from 'react-hot-toast';

export const DetectionsListPage: React.FC = () => {
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<IDetectionFilters>({
    ordering: '-detection_date',
  });
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid');
  const [selectedDetection, setSelectedDetection] = useState<Detection | null>(null);

  // Requête pour les détections
  const { data: detectionsData, isLoading, error } = useQuery({
    queryKey: ['detections', filters],
    queryFn: () => detectionService.getDetections(filters),
    refetchInterval: 30000, // Actualiser toutes les 30 secondes
  });

  // Requête pour les statistiques
  const { data: statsData } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: () => statsService.getDashboardStats(),
    refetchInterval: 60000, // Actualiser toutes les minutes
  });

  // Mutation pour la validation
  const validateMutation = useMutation({
    mutationFn: ({ id, status, comment }: { id: number; status: string; comment?: string }) =>
      detectionService.validateDetection(id, status, comment),
    onSuccess: (data) => {
      toast.success(data.message || 'Détection validée avec succès');
      queryClient.invalidateQueries({ queryKey: ['detections'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erreur lors de la validation');
    },
  });

  // Statistiques calculées à partir des données du dashboard
  const stats = useMemo(() => {
    if (!statsData) return null;

    // Utilisation des statistiques globales du dashboard
    const total = statsData.total_detections;
    const highConfidence = statsData.high_confidence_detections;

    // Calcul approximatif des détections en attente et validées
    // basé sur les détections actuellement affichées
    let pending = 0;
    let validated = 0;

    if (detectionsData?.results) {
      const detections = detectionsData.results;
      pending = detections.filter((d: any) => d.validation_status === 'DETECTED').length;
      validated = detections.filter((d: any) => d.validation_status === 'VALIDATED').length;
    }

    return { total, pending, validated, highConfidence };
  }, [statsData, detectionsData]);

  const handleFiltersChange = (newFilters: IDetectionFilters) => {
    setFilters(newFilters);
  };

  const handleFiltersReset = () => {
    setFilters({ ordering: '-detection_date' });
  };

  const handleValidateDetection = (detection: Detection, status: string) => {
    validateMutation.mutate({
      id: detection.id,
      status,
    });
  };

  const handleViewDetails = (detection: Detection) => {
    setSelectedDetection(detection);
    // TODO: Implémenter la navigation vers la page de détail
    window.location.href = `/detections/${detection.id}`;
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-slate-900 mb-2">
            Erreur de chargement
          </h2>
          <p className="text-slate-600">
            Impossible de charger les détections. Veuillez réessayer.
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Détections</h1>
            <p className="text-slate-600 mt-1">
              Surveillance et validation des détections d'orpaillage
            </p>
          </div>

          <div className="flex items-center space-x-3">
            {/* Sélecteur de vue */}
            <div className="flex bg-slate-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-slate-900 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                <ListBulletIcon className="w-4 h-4 inline mr-1" />
                Liste
              </button>
              <button
                onClick={() => setViewMode('map')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'map'
                    ? 'bg-white text-slate-900 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                <MapIcon className="w-4 h-4 inline mr-1" />
                Carte
              </button>
            </div>

            {permissions.canLaunchAnalysis() && (
              <Button
                variant="primary"
                className="flex items-center space-x-2"
                onClick={() => {
                  // TODO: Naviguer vers l'analyse spectrale
                  toast('Redirection vers l\'analyse spectrale...', {
                    icon: 'ℹ️',
                    duration: 3000,
                  });
                }}
              >
                <PlusIcon className="w-4 h-4" />
                <span>Nouvelle analyse</span>
              </Button>
            )}
          </div>
        </div>
      </motion.div>

      {/* Statistiques */}
      {stats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <StatCard
            title="Total détections"
            value={stats.total.toLocaleString()}
            icon={ChartBarIcon}
            color="blue"
          />
          <StatCard
            title="En attente"
            value={stats.pending.toLocaleString()}
            icon={ChartBarIcon}
            color="orange"
            trend={stats.pending > 0 ? { value: stats.pending, isPositive: false } : undefined}
          />
          <StatCard
            title="Validées"
            value={stats.validated.toLocaleString()}
            icon={ChartBarIcon}
            color="green"
          />
          <StatCard
            title="Haute confiance"
            value={stats.highConfidence.toLocaleString()}
            icon={ChartBarIcon}
            color="purple"
          />
        </motion.div>
      )}

      {/* Filtres */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <DetectionFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onReset={handleFiltersReset}
          isLoading={isLoading}
        />
      </motion.div>

      {/* Contenu principal */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        {viewMode === 'grid' ? (
          // Vue en grille
          <div>
            {detectionsData?.results && detectionsData.results.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {detectionsData.results.map((detection, index) => (
                    <motion.div
                      key={detection.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <DetectionCard
                        detection={detection}
                        onViewDetails={handleViewDetails}
                        onValidate={permissions.canValidateDetections() ? handleValidateDetection : undefined}
                        isLoading={validateMutation.isPending}
                      />
                    </motion.div>
                  ))}
                </div>

                {/* Pagination */}
                {detectionsData.count > detectionsData.results.length && (
                  <div className="mt-8 flex justify-center">
                    <Button variant="outline">
                      Charger plus de détections
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <Card className="p-8 text-center">
                <h3 className="text-lg font-semibold text-slate-900 mb-2">
                  Aucune détection trouvée
                </h3>
                <p className="text-slate-600 mb-4">
                  Aucune détection ne correspond à vos critères de recherche.
                </p>
                <Button variant="outline" onClick={handleFiltersReset}>
                  Réinitialiser les filtres
                </Button>
              </Card>
            )}
          </div>
        ) : (
          // Vue carte
          <div className="h-96">
            <DetectionMap
              detections={detectionsData?.results || []}
              selectedDetection={selectedDetection}
              onDetectionSelect={handleViewDetails}
              isLoading={isLoading}
            />
          </div>
        )}
      </motion.div>
    </div>
  );
};
