import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  BeakerIcon,
  PlayIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  MapPinIcon,
  CameraIcon,
  ShieldCheckIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import analysisService from '../services/analysis.service';
import type { AnalysisRequest, AnalysisResult } from '../services/analysis.service';
import { Card, Button, Loading } from '../components/ui';
import toast from 'react-hot-toast';

// Composant pour afficher les résultats d'analyse
const AnalysisResultCard: React.FC<{ result: AnalysisResult }> = ({ result }) => {
  // Protection contre les données manquantes
  if (!result || !result.data) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          <ExclamationTriangleIcon className="w-8 h-8 mx-auto mb-2" />
          <p>Données d'analyse incomplètes</p>
        </div>
      </Card>
    );
  }

  const data = result.data;

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Résultats de l'analyse</h3>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          result.success
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          <CheckCircleIcon className="w-3 h-3 mr-1" />
          {result.success ? 'Terminée' : 'Échec'}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center">
            <CameraIcon className="w-5 h-5 text-blue-500 mr-2" />
            <div>
              <p className="text-sm text-blue-600">Images traitées</p>
              <p className="text-xl font-bold text-blue-900">{data.images_processed || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="flex items-center">
            <MapPinIcon className="w-5 h-5 text-yellow-500 mr-2" />
            <div>
              <p className="text-sm text-yellow-600">Détections trouvées</p>
              <p className="text-xl font-bold text-yellow-900">{data.detections_found || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-center">
            <ShieldCheckIcon className="w-5 h-5 text-red-500 mr-2" />
            <div>
              <p className="text-sm text-red-600">Alertes générées</p>
              <p className="text-xl font-bold text-red-900">{data.alerts_generated || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center">
            <BanknotesIcon className="w-5 h-5 text-green-500 mr-2" />
            <div>
              <p className="text-sm text-green-600">Investigations créées</p>
              <p className="text-xl font-bold text-green-900">{data.investigations_created || 0}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Détails de l'analyse</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Statut :</span>
            <span className="ml-2 font-semibold text-green-600">
              {result.success ? 'Terminée avec succès' : 'Échec'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Message :</span>
            <span className="ml-2">{result.message}</span>
          </div>
          <div>
            <span className="text-gray-600">Risques financiers :</span>
            <span className="ml-2">{data.financial_risks_calculated || 0}</span>
          </div>
          <div>
            <span className="text-gray-600">Date d'analyse :</span>
            <span className="ml-2">
              {data.analysis_date ? new Date(data.analysis_date).toLocaleDateString('fr-FR') : 'Non disponible'}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export const AnalysisPage: React.FC = () => {
  const { user } = useAuth();
  const permissions = usePermissions();

  // États locaux
  const [monthsBack, setMonthsBack] = useState<number>(3);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  // Vérification des permissions
  if (!permissions.canLaunchAnalysis()) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous n'avez pas les permissions nécessaires pour accéder au module d'analyse spectrale.
          </p>
        </Card>
      </div>
    );
  }

  // Mutation pour lancer une analyse
  const runAnalysisMutation = useMutation({
    mutationFn: (request: AnalysisRequest) => analysisService.runAnalysis(request),
    onSuccess: (result: AnalysisResult) => {
      setAnalysisResult(result);
      toast.success('Analyse terminée avec succès !');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du lancement de l\'analyse');
    }
  });

  const handleRunAnalysis = () => {
    // Validation
    const validation = analysisService.validateAnalysisRequest({ months_back: monthsBack });
    if (!validation.valid) {
      toast.error(validation.errors.join(', '));
      return;
    }

    runAnalysisMutation.mutate({ months_back: monthsBack });
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-6 text-white shadow-xl"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Analyse d'Orpaillage Satellite
            </h1>
            <p className="text-purple-100">
              Détection automatisée d'activités d'orpaillage illégal en Côte d'Ivoire
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
              <BeakerIcon className="w-8 h-8 text-purple-900" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Formulaire de lancement d'analyse */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <PlayIcon className="w-5 h-5 mr-2 text-purple-500" />
            Lancer une nouvelle analyse
          </h2>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-blue-900 mb-2">À propos de l'analyse</h3>
            <p className="text-sm text-blue-700">
              Cette analyse traite automatiquement les images satellites des derniers mois pour détecter
              les activités d'orpaillage illégal dans la région de Bondoukou. L'algorithme utilise
              l'intelligence artificielle pour identifier les changements de terrain suspects.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Période d'analyse (mois)
              </label>
              <select
                value={monthsBack}
                onChange={(e) => setMonthsBack(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={runAnalysisMutation.isPending}
              >
                <option value={1}>1 mois (analyse rapide)</option>
                <option value={3}>3 mois (recommandé)</option>
                <option value={6}>6 mois (analyse approfondie)</option>
                <option value={12}>12 mois (analyse complète)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Plus la période est longue, plus l'analyse sera précise mais prendra du temps.
              </p>
            </div>

            <div className="flex items-end">
              <Button
                onClick={handleRunAnalysis}
                disabled={runAnalysisMutation.isPending}
                className="w-full flex items-center justify-center space-x-2"
                size="lg"
              >
                {runAnalysisMutation.isPending ? (
                  <>
                    <Loading />
                    <span>Analyse en cours...</span>
                  </>
                ) : (
                  <>
                    <PlayIcon className="w-5 h-5" />
                    <span>Lancer l'analyse</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Affichage des résultats */}
      {analysisResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <AnalysisResultCard result={analysisResult} />
        </motion.div>
      )}


    </div>
  );
};
