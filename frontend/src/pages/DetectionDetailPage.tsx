import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pop<PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import {
  ArrowLeftIcon,
  MapPinIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  PhotoIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { usePermissions } from '../hooks/usePermissions';
import { Card, Button, Loading, Badge, Modal } from '../components/ui';
import { CreateInvestigationModal } from '../components/CreateInvestigationModal';
import detectionService from '../services/detection.service';
import type { Detection } from '../types/detection.types.js';
import {
  DETECTION_TYPE_LABELS,
  VALIDATION_STATUS_LABELS,
  DETECTION_TYPE_COLORS,
  VALIDATION_STATUS_COLORS,
  getConfidenceLevel,
  getDetectionTypeIcon,
  getValidationStatusIcon,
} from '../types/detection.types.js';
import toast from 'react-hot-toast';

// Configuration de l'icône par défaut de Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Icône personnalisée pour les détections
const detectionIcon = new L.Icon({
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
  className: 'detection-marker'
});

export const DetectionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const permissions = usePermissions();
  const queryClient = useQueryClient();

  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationStatus, setValidationStatus] = useState<string>('');
  const [validationComment, setValidationComment] = useState('');
  const [showInvestigationModal, setShowInvestigationModal] = useState(false);

  // Requête pour la détection
  const { data: detection, isLoading, error } = useQuery({
    queryKey: ['detection', id],
    queryFn: () => detectionService.getDetection(Number(id)),
    enabled: !!id,
  });

  // Mutation pour la validation
  const validateMutation = useMutation({
    mutationFn: ({ status, comment }: { status: string; comment?: string }) =>
      detectionService.validateDetection(Number(id), status, comment),
    onSuccess: (data) => {
      toast.success(data.message || 'Détection validée avec succès');
      queryClient.invalidateQueries({ queryKey: ['detection', id] });
      queryClient.invalidateQueries({ queryKey: ['detections'] });
      setShowValidationModal(false);
      setValidationComment('');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erreur lors de la validation');
    },
  });

  // Mutation pour télécharger le rapport
  const downloadReportMutation = useMutation({
    mutationFn: () => detectionService.downloadReport(Number(id)),
    onSuccess: () => {
      toast.success('Rapport téléchargé avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du téléchargement du rapport');
    },
  });

  const handleValidation = () => {
    if (!validationStatus) return;

    validateMutation.mutate({
      status: validationStatus,
      comment: validationComment.trim() || undefined,
    });
  };

  const openValidationModal = (status: string) => {
    setValidationStatus(status);
    setShowValidationModal(true);
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error || !detection) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-slate-900 mb-2">
            Détection introuvable
          </h2>
          <p className="text-slate-600 mb-4">
            La détection demandée n'existe pas ou vous n'avez pas les permissions pour la voir.
          </p>
          <Button variant="outline" onClick={() => navigate('/detections')}>
            Retour aux détections
          </Button>
        </Card>
      </div>
    );
  }

  const confidenceLevel = getConfidenceLevel(detection.confidence_score);
  const canValidate = permissions.canValidateDetections() &&
                     detection.validation_status === 'DETECTED';

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* En-tête */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-4 mb-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/detections')}
            className="flex items-center space-x-2"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>Retour</span>
          </Button>
          <div className="h-6 w-px bg-slate-300"></div>
          <h1 className="text-2xl font-bold text-slate-900">
            Détection #{detection.id}
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">
              {getDetectionTypeIcon(detection.detection_type)}
            </span>
            <span className="text-lg font-medium text-slate-900">
              {DETECTION_TYPE_LABELS[detection.detection_type]}
            </span>
          </div>
          <Badge
            variant={VALIDATION_STATUS_COLORS[detection.validation_status] as any}
          >
            {getValidationStatusIcon(detection.validation_status)} {VALIDATION_STATUS_LABELS[detection.validation_status]}
          </Badge>
          <Badge variant={confidenceLevel.color as any}>
            Confiance: {(detection.confidence_score * 100).toFixed(0)}%
          </Badge>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Colonne principale */}
        <div className="lg:col-span-2 space-y-6">
          {/* Informations générales */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-4">
                Informations générales
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Localisation
                  </label>
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-900">{detection.region_name}</span>
                  </div>
                  <div className="text-sm text-slate-500 mt-1">
                    {detection.latitude.toFixed(6)}, {detection.longitude.toFixed(6)}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Date de détection
                  </label>
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-900">
                      {new Date(detection.detection_date).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: 'long',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Surface estimée
                  </label>
                  <span className="text-lg font-semibold text-slate-900">
                    {detection.area_hectares.toFixed(2)} hectares
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Version algorithme
                  </label>
                  <span className="text-slate-900 font-mono text-sm">
                    {detection.algorithm_version}
                  </span>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Indices spectraux */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-4">
                Analyse spectrale
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold text-green-600">
                      {detection.ndvi_anomaly_score?.toFixed(2) || 'N/A'}
                    </span>
                  </div>
                  <h3 className="font-medium text-slate-900">NDVI</h3>
                  <p className="text-sm text-slate-600">Indice de végétation</p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold text-blue-600">
                      {detection.ndwi_anomaly_score?.toFixed(2) || 'N/A'}
                    </span>
                  </div>
                  <h3 className="font-medium text-slate-900">NDWI</h3>
                  <p className="text-sm text-slate-600">Indice d'eau</p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold text-orange-600">
                      {detection.ndti_anomaly_score?.toFixed(2) || 'N/A'}
                    </span>
                  </div>
                  <h3 className="font-medium text-slate-900">NDTI</h3>
                  <p className="text-sm text-slate-600">Indice de sol nu</p>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Localisation sur carte */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-slate-900">
                  Localisation exacte
                </h2>
                <Badge variant="secondary" className="text-xs">
                  Lecture seule
                </Badge>
              </div>

              <div className="h-64 rounded-lg overflow-hidden border border-slate-200 relative">
                <MapContainer
                  center={[detection.latitude, detection.longitude]}
                  zoom={15}
                  style={{ height: '100%', width: '100%' }}
                  zoomControl={false}
                  dragging={false}
                  touchZoom={false}
                  doubleClickZoom={false}
                  scrollWheelZoom={false}
                  boxZoom={false}
                  keyboard={false}
                  attributionControl={true}
                >
                  {/* Overlay pour désactiver les interactions */}
                  <div
                    className="absolute inset-0 z-[1000] cursor-default"
                    style={{ pointerEvents: 'none' }}
                  />
                  <TileLayer
                    url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                    attribution='&copy; Esri, Maxar, Earthstar Geographics, and the GIS User Community'
                  />
                  <TileLayer
                    url="https://server.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}"
                    attribution='&copy; Esri'
                    opacity={0.7}
                  />
                  <Marker
                    position={[detection.latitude, detection.longitude]}
                    icon={detectionIcon}
                  >
                    <Popup>
                      <div className="text-center">
                        <h3 className="font-semibold text-slate-900 mb-1">
                          Détection #{detection.id}
                        </h3>
                        <p className="text-sm text-slate-600 mb-2">
                          {DETECTION_TYPE_LABELS[detection.detection_type]}
                        </p>
                        <p className="text-xs text-slate-500">
                          {detection.latitude.toFixed(6)}, {detection.longitude.toFixed(6)}
                        </p>
                        <p className="text-xs text-slate-500">
                          Surface: {detection.area_hectares.toFixed(2)} ha
                        </p>
                      </div>
                    </Popup>
                  </Marker>
                </MapContainer>
              </div>

              <div className="mt-4 text-sm text-slate-600">
                <div className="flex items-center justify-between">
                  <span>Coordonnées GPS:</span>
                  <span className="font-mono">
                    {detection.latitude.toFixed(6)}, {detection.longitude.toFixed(6)}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span>Image source:</span>
                  <span>{detection.image_name}</span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span>Date capture:</span>
                  <span>{new Date(detection.image_capture_date).toLocaleDateString('fr-FR')}</span>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions de validation */}
          {canValidate && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  Validation
                </h3>

                <div className="space-y-3">
                  <Button
                    variant="primary"
                    className="w-full flex items-center justify-center space-x-2"
                    onClick={() => openValidationModal('VALIDATED')}
                    disabled={validateMutation.isPending}
                  >
                    <CheckCircleIcon className="w-4 h-4" />
                    <span>Valider la détection</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => openValidationModal('FALSE_POSITIVE')}
                    disabled={validateMutation.isPending}
                  >
                    <XCircleIcon className="w-4 h-4" />
                    <span>Marquer faux positif</span>
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Informations de validation */}
          {detection.validated_by_name && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">
                  Historique de validation
                </h3>

                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      Validé par
                    </label>
                    <span className="text-slate-900">{detection.validated_by_name}</span>
                  </div>

                  {detection.validated_at && (
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">
                        Date de validation
                      </label>
                      <span className="text-slate-900">
                        {new Date(detection.validated_at).toLocaleString('fr-FR')}
                      </span>
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Actions supplémentaires */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                Actions
              </h3>

              <div className="space-y-3">
                {/* Bouton de génération de rapport - visible seulement si détection validée */}
                {detection.validation_status === 'VALIDATED' ? (
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2"
                    onClick={() => downloadReportMutation.mutate()}
                    disabled={downloadReportMutation.isPending}
                  >
                    <DocumentTextIcon className="w-4 h-4" />
                    <span>
                      {downloadReportMutation.isPending ? 'Génération...' : 'Télécharger rapport PDF'}
                    </span>
                  </Button>
                ) : (
                  <div className="relative">
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center space-x-2 opacity-50 cursor-not-allowed"
                      disabled
                    >
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Rapport non disponible</span>
                    </Button>
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                      <div className="bg-slate-800 text-white text-xs px-2 py-1 rounded shadow-lg">
                        La détection doit être validée
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-2"
                  onClick={() => setShowInvestigationModal(true)}
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Créer investigation</span>
                </Button>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>

      {/* Modal de validation */}
      <Modal
        isOpen={showValidationModal}
        onClose={() => setShowValidationModal(false)}
        title="Validation de la détection"
      >
        <div className="space-y-4">
          <p className="text-slate-600">
            Vous êtes sur le point de marquer cette détection comme{' '}
            <span className="font-medium">
              {validationStatus === 'VALIDATED' ? 'validée' : 'faux positif'}
            </span>.
          </p>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Commentaire (optionnel)
            </label>
            <textarea
              value={validationComment}
              onChange={(e) => setValidationComment(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Ajoutez un commentaire sur votre décision..."
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="primary"
              onClick={handleValidation}
              loading={validateMutation.isPending}
              className="flex-1"
            >
              Confirmer
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowValidationModal(false)}
              className="flex-1"
            >
              Annuler
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal de création d'investigation */}
      <CreateInvestigationModal
        isOpen={showInvestigationModal}
        onClose={() => setShowInvestigationModal(false)}
        detectionId={detection.id}
        detectionInfo={{
          type: DETECTION_TYPE_LABELS[detection.detection_type],
          region: detection.region_name,
          coordinates: `${detection.latitude.toFixed(6)}, ${detection.longitude.toFixed(6)}`
        }}
      />
    </div>
  );
};
