import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import {
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import detectionService from '../services/detection.service';
import { DetectionsMap } from '../components/DetectionsMap';
import { Card, Badge } from '../components/ui';
import {
  DETECTION_TYPE_LABELS,
  VALIDATION_STATUS_LABELS,
  DETECTION_TYPE_COLORS,
  VALIDATION_STATUS_COLORS,
  getConfidenceLevel,
  getDetectionTypeIcon,
  getValidationStatusIcon,
} from '../types/detection.types';
import {
  Box,
  CircularProgress,
  Alert,
} from '@mui/material';



export const DetectionsPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');

  const { data: detections, isLoading, error } = useQuery({
    queryKey: ['detections'],
    queryFn: () => detectionService.getDetections(),
    onSuccess: (data) => {
      console.log('Détections récupérées:', data);
    },
    onError: (error) => {
      console.error('Erreur lors de la récupération des détections:', error);
    }
  });

  const filteredDetections = detections?.results?.filter(detection => {
    const matchesSearch = (detection.detection_type || '').toLowerCase().includes(searchTerm.toLowerCase()) || // Corrected: detection_type
                         (detection.region_name || '').toLowerCase().includes(searchTerm.toLowerCase()); // Corrected: region_name
    const matchesType = typeFilter === 'all' || detection.detection_type === typeFilter; // Corrected: detection_type
    return matchesSearch && matchesType;
  });

  const handleDetectionClick = (detection: any) => {
    navigate(`/detections/${detection.id}`);
  };

  // Debug
  console.log('Détections brutes:', detections);
  console.log('Détections filtrées:', filteredDetections);
  console.log('Nombre de détections filtrées:', filteredDetections?.length);

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Erreur lors du chargement des détections
      </Alert>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* En-tête */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-slate-900 mb-2">
          Détections
        </h1>
        <p className="text-slate-600">
          Surveillance et analyse des détections d'orpaillage illégal
        </p>
      </div>

      {/* Filtres */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <input
              type="text"
              placeholder="Rechercher par type ou région..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="sm:w-48">
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tous les types</option>
            <option value="MINING_SITE">Exploitation minière</option>
            <option value="DEFORESTATION">Déforestation</option>
            <option value="CONSTRUCTION">Construction</option>
          </select>
        </div>
      </div>

      {/* Grande carte avec toutes les détections */}
      <DetectionsMap
        detections={filteredDetections || []}
        onDetectionClick={handleDetectionClick}
        height="70vh"
      />

      {/* Message de debug */}
      {!isLoading && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            Debug: {detections?.count || 0} détections totales, {filteredDetections?.length || 0} après filtrage
          </p>
        </div>
      )}

    </div>
  );
};