import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'presentation/providers/auth_provider.dart';
import 'presentation/theme/app_theme.dart';
import 'core/navigation/app_router.dart';

void main() {
  runApp(const GoldSentinelApp());
}

class GoldSentinelApp extends StatelessWidget {
  const GoldSentinelApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (context) => AuthProvider())],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final router = AppRouter.createRouter();

          return MaterialApp.router(
            title: 'Gold Sentinel',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            routerConfig: router,
          );
        },
      ),
    );
  }
}
