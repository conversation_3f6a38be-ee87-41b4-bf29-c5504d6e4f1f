import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../presentation/providers/auth_provider.dart';
import '../../presentation/pages/splash_page.dart';
import '../../presentation/pages/login_page.dart';
import '../../presentation/pages/dashboard_page.dart';
import '../../presentation/pages/alerts_page.dart';
import '../../presentation/pages/investigations_page.dart';
import '../../presentation/pages/profile_page.dart';
import '../../presentation/pages/main_navigation_page.dart';

class AppRouter {
  static const String splash = '/';
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String alerts = '/alerts';
  static const String alertDetail = '/alerts/:id';
  static const String investigations = '/investigations';
  static const String investigationDetail = '/investigations/:id';
  static const String profile = '/profile';
  static const String settings = '/settings';

  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: splash,
      debugLogDiagnostics: true,
      redirect: (context, state) {
        final authProvider = context.read<AuthProvider>();
        final isAuthenticated = authProvider.isAuthenticated;
        final isInitialized = authProvider.status != AuthStatus.initial;

        // If not initialized yet, stay on splash
        if (!isInitialized) {
          return splash;
        }

        // If on splash and initialized, redirect based on auth status
        if (state.uri.path == splash) {
          return isAuthenticated ? dashboard : login;
        }

        // If not authenticated and trying to access protected routes
        if (!isAuthenticated && !_isPublicRoute(state.uri.path)) {
          return login;
        }

        // If authenticated and trying to access login
        if (isAuthenticated && state.uri.path == login) {
          return dashboard;
        }

        return null; // No redirect needed
      },
      routes: [
        // Splash route
        GoRoute(
          path: splash,
          name: 'splash',
          builder: (context, state) => const SplashPage(),
        ),

        // Login route
        GoRoute(
          path: login,
          name: 'login',
          builder: (context, state) => const LoginPage(),
        ),

        // Main navigation shell with bottom navigation
        ShellRoute(
          builder: (context, state, child) {
            return MainNavigationPage(child: child);
          },
          routes: [
            // Dashboard route
            GoRoute(
              path: dashboard,
              name: 'dashboard',
              builder: (context, state) => const DashboardPage(),
            ),

            // Alerts routes
            GoRoute(
              path: alerts,
              name: 'alerts',
              builder: (context, state) => const AlertsPage(),
              routes: [
                GoRoute(
                  path: ':id',
                  name: 'alert-detail',
                  builder: (context, state) {
                    final alertId = int.parse(state.pathParameters['id']!);
                    return AlertDetailPage(alertId: alertId);
                  },
                ),
              ],
            ),

            // Investigations routes
            GoRoute(
              path: investigations,
              name: 'investigations',
              builder: (context, state) => const InvestigationsPage(),
              routes: [
                GoRoute(
                  path: ':id',
                  name: 'investigation-detail',
                  builder: (context, state) {
                    final investigationId = int.parse(
                      state.pathParameters['id']!,
                    );
                    return InvestigationDetailPage(
                      investigationId: investigationId,
                    );
                  },
                ),
              ],
            ),

            // Profile route
            GoRoute(
              path: profile,
              name: 'profile',
              builder: (context, state) => const ProfilePage(),
            ),
          ],
        ),
      ],
      errorBuilder:
          (context, state) => Scaffold(
            appBar: AppBar(title: const Text('Erreur')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Page non trouvée',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'La page "${state.uri.path}" n\'existe pas.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.go(dashboard),
                    child: const Text('Retour au tableau de bord'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  static bool _isPublicRoute(String location) {
    const publicRoutes = [splash, login];
    return publicRoutes.contains(location);
  }
}

// Extension for easier navigation
extension AppRouterExtension on GoRouter {
  void goToLogin() => go(AppRouter.login);
  void goToDashboard() => go(AppRouter.dashboard);
  void goToAlerts() => go(AppRouter.alerts);
  void goToInvestigations() => go(AppRouter.investigations);
  void goToProfile() => go(AppRouter.profile);

  void goToAlertDetail(int alertId) => go('${AppRouter.alerts}/$alertId');
  void goToInvestigationDetail(int investigationId) =>
      go('${AppRouter.investigations}/$investigationId');
}

// Placeholder pages (to be implemented)
class AlertDetailPage extends StatelessWidget {
  final int alertId;

  const AlertDetailPage({super.key, required this.alertId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Alerte #$alertId')),
      body: Center(child: Text('Détail de l\'alerte #$alertId')),
    );
  }
}

class InvestigationDetailPage extends StatelessWidget {
  final int investigationId;

  const InvestigationDetailPage({super.key, required this.investigationId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Investigation #$investigationId')),
      body: Center(child: Text('Détail de l\'investigation #$investigationId')),
    );
  }
}
