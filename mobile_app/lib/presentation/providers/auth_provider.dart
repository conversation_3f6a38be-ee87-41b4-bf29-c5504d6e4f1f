import 'package:flutter/foundation.dart';
import '../../data/datasources/auth_datasource_simple.dart';
import '../../data/models/user_model_simple.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  final AuthDataSource _authDataSource;

  AuthProvider({AuthDataSource? authDataSource})
    : _authDataSource = authDataSource ?? AuthDataSourceImpl();

  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;

  // User role getters
  bool get isAdministrator => _user?.isAdministrator ?? false;
  bool get isRegionalManager => _user?.isRegionalManager ?? false;
  bool get isFieldAgent => _user?.isFieldAgent ?? false;
  bool get isTechnicalAgent => _user?.isTechnicalAgent ?? false;
  bool get isAnalystAgent => _user?.isAnalystAgent ?? false;

  // Permission getters
  bool get canManageInvestigations => _user?.canManageInvestigations ?? false;
  bool get canValidateDetections => _user?.canValidateDetections ?? false;
  bool get canRunAnalysis => _user?.canRunAnalysis ?? false;

  // Initialize auth state
  Future<void> initialize() async {
    _setLoading(true);
    try {
      final isLoggedIn = await _authDataSource.isLoggedIn();
      if (isLoggedIn) {
        final cachedUser = await _authDataSource.getCachedUser();
        if (cachedUser != null) {
          _user = cachedUser;
          _status = AuthStatus.authenticated;
        } else {
          // Try to fetch fresh user data
          try {
            _user = await _authDataSource.getProfile();
            _status = AuthStatus.authenticated;
          } catch (e) {
            _status = AuthStatus.unauthenticated;
            _errorMessage = 'Session expirée. Veuillez vous reconnecter.';
          }
        }
      } else {
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      _status = AuthStatus.error;
      _errorMessage = 'Erreur lors de l\'initialisation: ${e.toString()}';
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      await _authDataSource.login(email, password);
      _user = await _authDataSource.getProfile();
      _status = AuthStatus.authenticated;
      notifyListeners();
      return true;
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = _extractErrorMessage(e.toString());
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);
    try {
      await _authDataSource.logout();
    } catch (e) {
      // Continue with logout even if API call fails
    } finally {
      _user = null;
      _status = AuthStatus.unauthenticated;
      _clearError();
      _setLoading(false);
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      _user = await _authDataSource.updateProfile(data);
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = _extractErrorMessage(e.toString());
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    _setLoading(true);
    _clearError();

    try {
      await _authDataSource.changePassword(oldPassword, newPassword);
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = _extractErrorMessage(e.toString());
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    if (!isAuthenticated) return;

    try {
      _user = await _authDataSource.getProfile();
      notifyListeners();
    } catch (e) {
      // If refresh fails, user might need to login again
      if (e.toString().contains('401') ||
          e.toString().contains('Non autorisé')) {
        await logout();
      }
    }
  }

  // Check if user has specific authority
  bool hasAuthority(String authority) {
    return _user?.hasAuthority(authority) ?? false;
  }

  // Check if user has any of the specified authorities
  bool hasAnyAuthority(List<String> authorities) {
    return _user?.hasAnyAuthority(authorities) ?? false;
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  String _extractErrorMessage(String error) {
    // Extract meaningful error messages
    if (error.contains('Identifiants incorrects')) {
      return 'Email ou mot de passe incorrect';
    } else if (error.contains('connexion')) {
      return 'Problème de connexion. Vérifiez votre internet.';
    } else if (error.contains('401')) {
      return 'Session expirée. Veuillez vous reconnecter.';
    } else if (error.contains('403')) {
      return 'Accès non autorisé';
    } else if (error.contains('500')) {
      return 'Erreur du serveur. Réessayez plus tard.';
    } else {
      return 'Une erreur est survenue. Réessayez.';
    }
  }

  // Clear error message
  void clearError() {
    _clearError();
    notifyListeners();
  }

  // Force logout (for when tokens are invalid)
  void forceLogout() {
    _user = null;
    _status = AuthStatus.unauthenticated;
    _clearError();
    notifyListeners();
  }
}
