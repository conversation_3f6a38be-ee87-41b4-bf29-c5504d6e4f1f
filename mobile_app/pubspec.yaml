name: gold_sentinel_mobile
description: "Application mobile Gold Sentinel pour la détection d'orpaillage illégal"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # State Management
  provider: ^6.1.1

  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Authentication & Storage
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.2

  # UI & Navigation
  go_router: ^12.1.3
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0

  # Maps
  flutter_map: ^6.1.0
  latlong2: ^0.9.1

  # Charts & Graphs
  fl_chart: ^0.65.0

  # Notifications
  flutter_local_notifications: ^16.3.0

  # Utils
  intl: ^0.19.0
  equatable: ^2.0.5

  # Icons
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
