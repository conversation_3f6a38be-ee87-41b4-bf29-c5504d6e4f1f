# This is a generated file; do not edit or check into version control.
flutter_local_notifications=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/
flutter_local_notifications_linux=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
flutter_secure_storage=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
path_provider=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
shared_preferences=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
sqflite_android=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
sqflite_darwin=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
