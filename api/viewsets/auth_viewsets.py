from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from api.serializers.auth_serializers import UserProfileSerializer


class AuthViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    """
    ViewSet pour l'authentification et profil utilisateur
    - GET /api/v1/auth/profile/ - Profil utilisateur connecté
    """

    @action(detail=False, methods=['get'], url_path='profile')
    def get_profile(self, request):
        """
        Récupère le profil de l'utilisateur connecté
        GET /api/v1/auth/profile/
        """
        try:
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': f'Erreur récupération profil: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['put'], url_path='profile')
    def update_profile(self, request):
        """
        Met à jour le profil de l'utilisateur connecté
        PUT /api/v1/auth/profile/
        """
        try:
            serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    'message': 'Profil mis à jour avec succès',
                    'data': serializer.data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Données invalides',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': f'Erreur mise à jour profil: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='change-password')
    def change_password(self, request):
        """
        Change le mot de passe de l'utilisateur connecté
        POST /api/v1/auth/change-password/
        """
        try:
            old_password = request.data.get('old_password')
            new_password = request.data.get('new_password')

            if not old_password or not new_password:
                return Response({
                    'error': 'Ancien et nouveau mot de passe requis'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier l'ancien mot de passe
            if not request.user.check_password(old_password):
                return Response({
                    'error': 'Ancien mot de passe incorrect'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Changer le mot de passe
            request.user.set_password(new_password)
            request.user.save()

            return Response({
                'message': 'Mot de passe changé avec succès'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Erreur changement mot de passe: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
