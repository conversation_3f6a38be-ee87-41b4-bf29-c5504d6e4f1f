from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from rest_framework import filters
from django.utils import timezone
from django.http import Http404

from alert.models.alert_model import AlertModel
from api.serializers.alert_serializer import AlertSerializer
from permissions.IsResponsableOrAgent import IsResponsableOrAgent
from account.models.user_model import UserModel

class AlertViewSet(viewsets.ModelViewSet):
    permission_classes = [IsResponsableOrAgent]
    """
    ViewSet pour les alertes d'orpaillage
    - GET /api/alerts/ - Liste alertes (filtrées selon le rôle)
    - GET /api/alerts/{id}/ - Détail alerte
    - PUT /api/alerts/{id}/ - Mise à jour alerte
    - PATCH /api/alerts/{id}/status/ - Mise à jour statut
    """
    serializer_class = AlertSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['level', 'alert_type', 'alert_status', 'region', 'is_read']
    ordering_fields = ['sent_at', 'level']
    ordering = ['-sent_at']

    # Blocage création manuelle d'alertes
    http_method_names = ['get', 'put', 'patch', 'head', 'options']

    def get_queryset(self):
        """
        Filtre les alertes selon le rôle de l'utilisateur :
        - Responsable Régional : toutes les alertes
        - Agent Terrain : uniquement les alertes assignées

        Note: Pour les actions spécifiques (assign, acknowledge, etc.),
        les Responsables ont accès à toutes les alertes
        """
        user = self.request.user

        # Vérifier si l'utilisateur est un Responsable Régional
        if user.user_authorities.filter(
            authority__name='Responsable Régional',
            status=True
        ).exists():
            # Responsable voit toutes les alertes
            return AlertModel.objects.all()

        # Agent Terrain voit uniquement ses alertes assignées
        elif user.user_authorities.filter(
            authority__name='Agent Terrain',
            status=True
        ).exists():
            return AlertModel.objects.filter(assigned_to=user)

        # Par défaut, aucune alerte (sécurité)
        return AlertModel.objects.none()

    def get_object(self):
        """
        Override get_object pour permettre aux Responsables d'accéder
        à toutes les alertes lors des actions (assign, acknowledge, etc.)
        """
        user = self.request.user

        # Pour les Responsables Régionaux, permettre l'accès à toutes les alertes
        if user.user_authorities.filter(
            authority__name='Responsable Régional',
            status=True
        ).exists():
            # Utiliser le queryset complet pour les responsables
            queryset = AlertModel.objects.all()
            lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
            filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}

            try:
                obj = queryset.get(**filter_kwargs)
                self.check_object_permissions(self.request, obj)
                return obj
            except AlertModel.DoesNotExist:
                raise Http404

        # Pour les autres utilisateurs, utiliser le comportement par défaut
        return super().get_object()

    @action(detail=True, methods=['patch'], url_path='status')
    def update_status(self, request, pk=None):
        """
        Met à jour le statut d'une alerte
        PATCH /api/alerts/{id}/status/
        Body: {"alert_status": "ACKNOWLEDGED", "assigned_to": user_id}
        """
        try:
            alert = self.get_object()
            new_status = request.data.get('alert_status')
            assigned_to = request.data.get('assigned_to')

            if new_status not in ['ACTIVE', 'ACKNOWLEDGED', 'RESOLVED', 'FALSE_ALARM']:
                return Response({
                    'error': 'alert_status doit être ACTIVE, ACKNOWLEDGED, RESOLVED ou FALSE_ALARM'
                }, status=status.HTTP_400_BAD_REQUEST)

            alert.alert_status = new_status
            alert.is_read = True

            if assigned_to:
                alert.assigned_to_id = assigned_to

            alert.save()

            serializer = self.get_serializer(alert)
            return Response({
                'message': 'Statut de l\'alerte mis à jour',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur mise à jour statut: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='active')
    def active_alerts(self, request):
        """
        Alertes actives non lues (filtrées selon le rôle)
        GET /api/alerts/active/
        """
        active_alerts = self.get_queryset().filter(
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED'],
            is_read=False
        ).order_by('-sent_at')

        serializer = self.get_serializer(active_alerts, many=True)
        return Response({
            'count': active_alerts.count(),
            'results': serializer.data
        })

    @action(detail=False, methods=['get'], url_path='critical')
    def critical_alerts(self, request):
        """
        Alertes critiques (filtrées selon le rôle)
        GET /api/alerts/critical/
        """
        critical_alerts = self.get_queryset().filter(
            level__in=['CRITICAL', 'HIGH'],
            alert_status='ACTIVE'
        ).order_by('-sent_at')

        serializer = self.get_serializer(critical_alerts, many=True)
        return Response({
            'count': critical_alerts.count(),
            'results': serializer.data
        })

    @action(detail=False, methods=['get'], url_path='assigned-to-me')
    def my_alerts(self, request):
        """
        Alertes assignées à l'utilisateur connecté
        GET /api/alerts/assigned-to-me/
        """
        if not request.user.is_authenticated:
            return Response({'error': 'Authentification requise'},
                            status=status.HTTP_401_UNAUTHORIZED)

        my_alerts = AlertModel.objects.filter(
            assigned_to=request.user,
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
        ).order_by('-sent_at')

        serializer = self.get_serializer(my_alerts, many=True)
        return Response({
            'count': my_alerts.count(),
            'results': serializer.data
        })

    @action(detail=True, methods=['patch'], url_path='assign')
    def assign_alert(self, request, pk=None):
        """
        Assigne une alerte à un agent
        PATCH /api/alerts/{id}/assign/
        Body: {"assigned_to": user_id, "notes": "optional notes"}
        """
        try:
            alert = self.get_object()
            assigned_to_id = request.data.get('assigned_to')
            notes = request.data.get('notes', '')

            if not assigned_to_id:
                return Response({
                    'error': 'assigned_to est requis'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier que l'alerte n'est pas déjà assignée
            if alert.assigned_to is not None:
                return Response({
                    'error': f'Alerte déjà assignée à {alert.assigned_to.get_full_name()}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier que l'utilisateur existe et est un agent terrain
            try:
                agent = UserModel.objects.get(
                    id=assigned_to_id,
                    is_active=True
                )

                # Vérifier que l'utilisateur a l'autorité Agent Terrain
                if not agent.user_authorities.filter(
                    authority__name='Agent Terrain',
                    status=True
                ).exists():
                    return Response({
                        'error': 'L\'utilisateur sélectionné n\'est pas un Agent Terrain'
                    }, status=status.HTTP_400_BAD_REQUEST)

            except UserModel.DoesNotExist:
                return Response({
                    'error': 'Agent introuvable ou inactif'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier que l'alerte peut être assignée
            if alert.alert_status not in ['ACTIVE', 'ACKNOWLEDGED']:
                return Response({
                    'error': f'Alerte déjà {alert.alert_status}. Seules les alertes ACTIVE ou ACKNOWLEDGED peuvent être assignées'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Calculer la charge de travail actuelle de l'agent
            current_alerts = AlertModel.objects.filter(
                assigned_to=agent,
                alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
            ).count()

            # Assigner l'alerte
            alert.assigned_to = agent
            alert.alert_status = 'ACKNOWLEDGED'  # Marquer comme accusée lors de l'assignation
            alert.is_read = True
            alert.save()

            # Ajouter une note d'assignation si fournie
            if notes:
                # Ici on pourrait ajouter un système de notes/commentaires
                pass

            serializer = self.get_serializer(alert)
            response_data = {
                'success': True,
                'message': f'Alerte assignée à {agent.get_full_name()}',
                'data': serializer.data,
                'agent_info': {
                    'name': agent.get_full_name(),
                    'current_alerts': current_alerts + 1
                }
            }

            # Avertissement si l'agent a beaucoup d'alertes
            if current_alerts >= 8:
                response_data['warning'] = f'Attention: {agent.get_full_name()} a maintenant {current_alerts + 1} alertes assignées'

            return Response(response_data)

        except Exception as e:
            return Response({
                'error': f'Erreur assignation: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['patch'], url_path='acknowledge')
    def acknowledge_alert(self, request, pk=None):
        """
        Accuse réception d'une alerte
        PATCH /api/alerts/{id}/acknowledge/
        Body: {"notes": "optional notes"}
        """
        try:
            alert = self.get_object()
            notes = request.data.get('notes', '')

            if alert.alert_status != 'ACTIVE':
                return Response({
                    'error': f'Alerte déjà {alert.alert_status}. Seules les alertes ACTIVE peuvent être accusées'
                }, status=status.HTTP_400_BAD_REQUEST)

            alert.alert_status = 'ACKNOWLEDGED'
            alert.is_read = True
            alert.save()

            serializer = self.get_serializer(alert)
            return Response({
                'success': True,
                'message': 'Alerte accusée avec succès',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur accusé réception: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['patch'], url_path='resolve')
    def resolve_alert(self, request, pk=None):
        """
        Résout une alerte
        PATCH /api/alerts/{id}/resolve/
        Body: {"notes": "resolution notes"}
        """
        try:
            alert = self.get_object()
            notes = request.data.get('notes', '')

            if not notes:
                return Response({
                    'error': 'Les notes de résolution sont requises'
                }, status=status.HTTP_400_BAD_REQUEST)

            if alert.alert_status == 'RESOLVED':
                return Response({
                    'error': 'Alerte déjà résolue'
                }, status=status.HTTP_400_BAD_REQUEST)

            alert.alert_status = 'RESOLVED'
            alert.is_read = True
            alert.save()

            serializer = self.get_serializer(alert)
            return Response({
                'success': True,
                'message': 'Alerte résolue avec succès',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur résolution: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['patch'], url_path='read')
    def mark_as_read(self, request, pk=None):
        """
        Marque une alerte comme lue/non lue
        PATCH /api/alerts/{id}/read/
        Body: {"is_read": true/false}
        """
        try:
            alert = self.get_object()
            is_read = request.data.get('is_read', True)

            alert.is_read = is_read
            alert.save()

            serializer = self.get_serializer(alert)
            return Response({
                'success': True,
                'message': f'Alerte marquée comme {"lue" if is_read else "non lue"}',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur marquage lecture: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='by-region')
    def alerts_by_region(self, request):
        """
        Alertes groupées par région pour la carte
        GET /api/alerts/by-region/
        """
        try:
            from collections import defaultdict

            # Récupérer toutes les alertes actives (filtrées selon le rôle)
            alerts = self.get_queryset().filter(
                alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
            ).select_related('region')

            # Grouper par région
            regions_data = defaultdict(lambda: {
                'region': '',
                'coordinates': [8.0, -3.0],  # Coordonnées par défaut Côte d'Ivoire
                'alerts': [],
                'count_by_level': {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
            })

            for alert in alerts:
                region_name = alert.region.name if alert.region else 'Région inconnue'

                # Initialiser la région si première fois
                if not regions_data[region_name]['region']:
                    regions_data[region_name]['region'] = region_name
                    # Coordonnées spécifiques par région
                    if region_name == 'BONDOUKOU':
                        regions_data[region_name]['coordinates'] = [8.0402, -2.8000]
                    elif region_name == 'BOUNA':
                        regions_data[region_name]['coordinates'] = [9.2667, -2.9833]
                    elif region_name == 'TANDA':
                        regions_data[region_name]['coordinates'] = [7.8000, -3.1667]
                    elif region_name == 'NASSIAN':
                        regions_data[region_name]['coordinates'] = [8.4500, -4.8833]

                # Ajouter l'alerte
                alert_data = self.get_serializer(alert).data
                regions_data[region_name]['alerts'].append(alert_data)

                # Compter par niveau
                level = alert.level
                if level in regions_data[region_name]['count_by_level']:
                    regions_data[region_name]['count_by_level'][level] += 1

            # Convertir en liste
            results = list(regions_data.values())

            return Response({
                'count': len(results),
                'results': results
            })

        except Exception as e:
            return Response({
                'error': f'Erreur récupération alertes par région: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='agents')
    def get_agents_workload(self, request):
        """
        Récupère la liste des agents avec leur charge de travail
        GET /api/alerts/agents/
        """
        try:
            # Récupérer tous les agents terrain (utilisateurs avec autorité Agent Terrain)
            agents = UserModel.objects.filter(
                is_active=True,
                user_authorities__authority__name='Agent Terrain',
                user_authorities__status=True
            ).distinct()

            agents_data = []
            for agent in agents:
                # Calculer la charge de travail actuelle
                current_alerts = AlertModel.objects.filter(
                    assigned_to=agent,
                    alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
                ).count()

                # Calculer les alertes résolues (pour statistiques)
                resolved_alerts = AlertModel.objects.filter(
                    assigned_to=agent,
                    alert_status='RESOLVED'
                ).count()

                agents_data.append({
                    'id': agent.id,
                    'name': agent.get_full_name(),
                    'email': agent.email,
                    'current_alerts': current_alerts,
                    'resolved_alerts': resolved_alerts,
                    'total_alerts': current_alerts + resolved_alerts,
                    'workload_status': 'HIGH' if current_alerts >= 8 else 'MEDIUM' if current_alerts >= 4 else 'LOW'
                })

            # Trier par charge de travail (moins chargé en premier)
            agents_data.sort(key=lambda x: x['current_alerts'])

            return Response({
                'count': len(agents_data),
                'results': agents_data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur récupération agents: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
