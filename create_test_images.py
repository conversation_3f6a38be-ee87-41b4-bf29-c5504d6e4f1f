#!/usr/bin/env python3
"""
Script pour créer des images de test dans la base de données Django
Exécuter depuis le répertoire racine du projet Django :
python create_test_images.py
"""

import os
import sys
import django
from datetime import date, timedelta
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gold_mining_detection.settings')
django.setup()

from image.models.image_model import ImageModel
from region.models.region_model import RegionModel

def create_test_images():
    """Créer des images de test pour chaque région"""
    
    print("🔍 Vérification des régions existantes...")
    regions = RegionModel.objects.all()
    
    if not regions.exists():
        print("❌ Aucune région trouvée. Création des régions de test...")
        # Créer les régions de base
        regions_data = [
            {'name': 'BONDOUKOU', 'code': 'BDK'},
            {'name': 'BOUNA', 'code': 'BNA'},
            {'name': 'TANDA', 'code': 'TDA'},
            {'name': 'NASSIAN', 'code': 'NSN'},
        ]
        
        for region_data in regions_data:
            region, created = RegionModel.objects.get_or_create(
                name=region_data['name'],
                defaults={'code': region_data['code']}
            )
            if created:
                print(f"✅ Région créée: {region.name}")
        
        regions = RegionModel.objects.all()
    
    print(f"✅ {regions.count()} région(s) trouvée(s)")
    
    # Supprimer les anciennes images de test
    test_images = ImageModel.objects.filter(name__startswith='Test Image')
    if test_images.exists():
        count = test_images.count()
        test_images.delete()
        print(f"🗑️ {count} ancienne(s) image(s) de test supprimée(s)")
    
    print("📸 Création des images de test...")
    
    # Créer des images de test pour chaque région
    satellites = ['SENTINEL_2', 'LANDSAT_8', 'SENTINEL_1']
    statuses = ['COMPLETED', 'PROCESSING', 'FAILED']
    
    images_created = 0
    
    for i, region in enumerate(regions):
        for j in range(5):  # 5 images par région
            capture_date = date.today() - timedelta(days=j*7)  # Une image par semaine
            satellite = satellites[j % len(satellites)]
            status = statuses[0] if j < 4 else statuses[j % len(statuses)]  # Plupart COMPLETED
            
            image = ImageModel.objects.create(
                name=f"Test Image {region.name} #{j+1}",
                capture_date=capture_date,
                satellite_source=satellite,
                cloud_coverage=float(10 + j * 5),  # 10%, 15%, 20%, etc.
                resolution=10.0,
                gee_asset_id=f"test_asset_{region.code.lower()}_{j+1}",
                gee_collection='COPERNICUS/S2_SR',
                processing_status=status,
                processed_at=capture_date if status == 'COMPLETED' else None,
                ndvi_mean=Decimal(str(0.3 + j * 0.1)) if status == 'COMPLETED' else None,
                ndwi_mean=Decimal(str(0.2 + j * 0.05)) if status == 'COMPLETED' else None,
                ndti_mean=Decimal(str(0.1 + j * 0.02)) if status == 'COMPLETED' else None,
                region=region,
                center_lat=8.0 + i * 0.1,  # Coordonnées légèrement différentes
                center_lon=-3.0 + i * 0.1,
            )
            
            images_created += 1
            print(f"✅ Image créée: {image.name} ({image.satellite_source}, {image.processing_status})")
    
    print(f"\n🎉 {images_created} images de test créées avec succès!")
    
    # Vérification finale
    total_images = ImageModel.objects.count()
    print(f"📊 Total d'images en base: {total_images}")
    
    # Afficher quelques statistiques
    by_region = {}
    for region in regions:
        count = ImageModel.objects.filter(region=region).count()
        by_region[region.name] = count
    
    print("\n📈 Répartition par région:")
    for region_name, count in by_region.items():
        print(f"  - {region_name}: {count} image(s)")
    
    by_status = {}
    for status in ['COMPLETED', 'PROCESSING', 'FAILED']:
        count = ImageModel.objects.filter(processing_status=status).count()
        by_status[status] = count
    
    print("\n📈 Répartition par statut:")
    for status, count in by_status.items():
        print(f"  - {status}: {count} image(s)")

def verify_api_structure():
    """Vérifier que la structure des données correspond à ce qu'attend le frontend"""
    print("\n🔍 Vérification de la structure API...")
    
    # Simuler ce que retourne l'API
    images = ImageModel.objects.all()[:5]
    
    print("📋 Structure d'une image:")
    if images:
        image = images[0]
        print(f"  - ID: {image.id}")
        print(f"  - Name: {image.name}")
        print(f"  - Capture Date: {image.capture_date}")
        print(f"  - Satellite: {image.satellite_source}")
        print(f"  - Region: {image.region.name}")
        print(f"  - Status: {image.processing_status}")
        print(f"  - Cloud Coverage: {image.cloud_coverage}%")
    
    print(f"\n✅ Structure API vérifiée. {images.count()} image(s) disponible(s).")

if __name__ == '__main__':
    try:
        create_test_images()
        verify_api_structure()
        print("\n🎯 Script terminé avec succès!")
        print("💡 Vous pouvez maintenant actualiser la page Images dans le frontend.")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de l'exécution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
