#!/usr/bin/env python3
"""
Test de connexion à PostgreSQL pour Gold Sentinel
"""

import os
import sys
import psycopg2
from dotenv import load_dotenv

def test_connection():
    # Charger les variables d'environnement
    load_dotenv()
    
    # Paramètres de connexion
    params = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'port': os.getenv('POSTGRES_PORT', '5432'),
        'database': os.getenv('POSTGRES_DB', 'gold_mining_detection'),
        'user': os.getenv('POSTGRES_USER', 'admin'),
        'password': os.getenv('POSTGRES_PASSWORD', 'admin')
    }
    
    print("🔍 TEST DE CONNEXION POSTGRESQL")
    print("=" * 40)
    print(f"Host: {params['host']}")
    print(f"Port: {params['port']}")
    print(f"Database: {params['database']}")
    print(f"User: {params['user']}")
    print()
    
    # Test de connexion
    try:
        print("📡 Tentative de connexion...")
        conn = psycopg2.connect(**params)
        print("✅ Connexion réussie !")
        
        # Test de requête
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"📊 Version PostgreSQL: {version[0]}")
        
        # Vérifier si la base existe et a des tables
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        table_count = cursor.fetchone()[0]
        print(f"📋 Nombre de tables: {table_count}")
        
        if table_count > 0:
            # Lister quelques tables
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name 
                LIMIT 10
            """)
            tables = cursor.fetchall()
            print("📝 Tables principales:")
            for table in tables:
                print(f"   - {table[0]}")
        
        cursor.close()
        conn.close()
        
        print()
        print("🎉 Base de données accessible et fonctionnelle !")
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ Erreur de connexion: {e}")
        print()
        print("🔧 Solutions possibles:")
        print("1. Vérifiez que PostgreSQL est démarré")
        print("2. Vérifiez les paramètres de connexion")
        print("3. Testez avec les paramètres de pgAdmin")
        return False
        
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_different_hosts():
    """Teste différentes configurations d'host"""
    hosts_to_test = [
        'localhost',
        '127.0.0.1',
        '**********',
        '**********',
        '**********'
    ]
    
    print("\n🔍 TEST DE DIFFÉRENTS HOSTS")
    print("=" * 40)
    
    for host in hosts_to_test:
        try:
            conn = psycopg2.connect(
                host=host,
                port=os.getenv('POSTGRES_PORT', '5432'),
                database=os.getenv('POSTGRES_DB', 'gold_mining_detection'),
                user=os.getenv('POSTGRES_USER', 'admin'),
                password=os.getenv('POSTGRES_PASSWORD', 'admin'),
                connect_timeout=3
            )
            print(f"✅ {host} - CONNEXION RÉUSSIE")
            conn.close()
            return host
        except:
            print(f"❌ {host} - Échec")
    
    return None

if __name__ == "__main__":
    # Test avec la configuration actuelle
    if not test_connection():
        print("\n" + "="*50)
        print("🔍 RECHERCHE DE L'HOST CORRECT...")
        
        # Charger les variables d'environnement
        load_dotenv()
        
        # Tester différents hosts
        working_host = test_different_hosts()
        
        if working_host:
            print(f"\n🎯 HOST FONCTIONNEL TROUVÉ: {working_host}")
            print(f"\n📝 Mettez à jour votre .env:")
            print(f"POSTGRES_HOST={working_host}")
        else:
            print("\n❌ Aucun host fonctionnel trouvé")
            print("\n🔧 Vérifiez:")
            print("1. Que PostgreSQL est démarré")
            print("2. Les paramètres de pgAdmin")
            print("3. Le port mapping Docker")
    
    print("\n" + "="*50)
