#!/bin/bash

# Script de démarrage frontend uniquement
# Pour tester l'interface sans problème de base de données

set -e

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Démarrage Frontend Gold Sentinel v3.0"
echo "========================================"

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm n'est pas installé"
    exit 1
fi

print_success "Node.js et npm détectés"

# Aller dans le dossier frontend
cd frontend

# Installer les dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    print_status "Installation des dépendances Node.js..."
    npm install
    print_success "Dépendances installées"
else
    print_success "Dépendances Node.js déjà installées"
fi

# Vérifier le fichier .env.development
if [ ! -f ".env.development" ]; then
    print_warning "Fichier .env.development manquant"
    print_status "Création du fichier .env.development..."
    cat > .env.development << EOF
# Configuration de développement pour Gold Sentinel Frontend
VITE_API_URL=http://localhost:8000/api/v1
VITE_APP_NAME=Gold Sentinel
VITE_APP_VERSION=3.0.0

# Configuration backend
VITE_BACKEND_URL=http://localhost:8000

# Configuration de développement
VITE_NODE_ENV=development
VITE_DEBUG=true

# Configuration des timeouts
VITE_API_TIMEOUT=30000

# Configuration des notifications
VITE_ENABLE_NOTIFICATIONS=true
VITE_NOTIFICATION_DURATION=4000
EOF
    print_success "Fichier .env.development créé"
fi

print_status "Démarrage du serveur de développement Vite..."
print_success "Frontend sera accessible sur http://localhost:5173"
print_warning "Note: Le backend doit être démarré séparément pour les API"

# Démarrer le serveur de développement
npm run dev
