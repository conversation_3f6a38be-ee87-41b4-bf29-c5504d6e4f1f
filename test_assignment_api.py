#!/usr/bin/env python3
"""
Script pour tester l'API d'assignation d'alertes
"""

import os
import sys
import django
import requests
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gold_mining_detection.settings')
django.setup()

from alert.models.alert_model import AlertModel
from account.models.user_model import UserModel
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken

def get_auth_token():
    """Obtenir un token d'authentification"""
    try:
        # Essayer avec l'admin
        user = authenticate(email='<EMAIL>', password='admin123')
        if user:
            refresh = RefreshToken.for_user(user)
            return str(refresh.access_token)
        
        # Essayer avec un agent
        agent = UserModel.objects.filter(role='AGENT', is_active=True).first()
        if agent:
            refresh = RefreshToken.for_user(agent)
            return str(refresh.access_token)
        
        print("❌ Aucun utilisateur trouvé pour l'authentification")
        return None
        
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return None

def test_assignment_endpoint():
    """Tester l'endpoint d'assignation"""
    print("🧪 Test de l'endpoint d'assignation...")
    
    # Obtenir un token
    token = get_auth_token()
    if not token:
        return False
    
    # Récupérer une alerte non assignée
    alert = AlertModel.objects.filter(assigned_to__isnull=True).first()
    if not alert:
        print("❌ Aucune alerte non assignée trouvée")
        return False
    
    # Récupérer un agent
    agent = UserModel.objects.filter(role='AGENT', is_active=True).first()
    if not agent:
        print("❌ Aucun agent trouvé")
        return False
    
    print(f"🎯 Test d'assignation:")
    print(f"  - Alerte ID: {alert.id}")
    print(f"  - Agent ID: {agent.id} ({agent.get_full_name()})")
    
    # Préparer la requête
    url = f"http://localhost:8000/api/v1/alerts/{alert.id}/assign/"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    data = {
        'assigned_to': agent.id,
        'notes': 'Test d\'assignation via script'
    }
    
    print(f"📡 Requête:")
    print(f"  - URL: {url}")
    print(f"  - Data: {data}")
    
    try:
        # Faire la requête
        response = requests.patch(url, headers=headers, json=data)
        
        print(f"📊 Réponse:")
        print(f"  - Status: {response.status_code}")
        print(f"  - Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès: {result}")
            return True
        else:
            print(f"❌ Erreur {response.status_code}:")
            try:
                error_data = response.json()
                print(f"  - Erreur JSON: {error_data}")
            except:
                print(f"  - Erreur texte: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur de requête: {e}")
        return False

def test_direct_assignment():
    """Tester l'assignation directe en Django"""
    print("\n🔧 Test d'assignation directe...")
    
    try:
        # Récupérer une alerte non assignée
        alert = AlertModel.objects.filter(assigned_to__isnull=True).first()
        if not alert:
            print("❌ Aucune alerte non assignée trouvée")
            return False
        
        # Récupérer un agent
        agent = UserModel.objects.filter(role='AGENT', is_active=True).first()
        if not agent:
            print("❌ Aucun agent trouvé")
            return False
        
        print(f"🎯 Assignation directe:")
        print(f"  - Alerte: {alert.title}")
        print(f"  - Agent: {agent.get_full_name()}")
        
        # Assigner directement
        alert.assigned_to = agent
        alert.alert_status = 'ACKNOWLEDGED'
        alert.is_read = True
        alert.save()
        
        print("✅ Assignation directe réussie")
        
        # Vérifier la charge de travail
        current_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
        ).count()
        
        print(f"📊 Charge de travail de {agent.get_full_name()}: {current_alerts} alertes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'assignation directe: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_viewset_method():
    """Vérifier la méthode assign_alert du ViewSet"""
    print("\n🔍 Vérification de la méthode assign_alert...")
    
    try:
        from api.viewsets.alert_viewsets import AlertViewSet
        
        # Vérifier que la méthode existe
        if hasattr(AlertViewSet, 'assign_alert'):
            print("✅ Méthode assign_alert trouvée")
            
            # Vérifier la signature
            import inspect
            sig = inspect.signature(AlertViewSet.assign_alert)
            print(f"📝 Signature: {sig}")
            
            return True
        else:
            print("❌ Méthode assign_alert non trouvée")
            return False
            
    except Exception as e:
        print(f"❌ Erreur de vérification: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Test complet de l'assignation d'alertes\n")
    
    # Vérifier les données
    agents = UserModel.objects.filter(role='AGENT', is_active=True)
    alerts = AlertModel.objects.filter(assigned_to__isnull=True)
    
    print(f"📊 État des données:")
    print(f"  - Agents disponibles: {agents.count()}")
    print(f"  - Alertes non assignées: {alerts.count()}")
    
    if agents.count() == 0 or alerts.count() == 0:
        print("\n❌ Données insuffisantes pour le test")
        print("💡 Exécutez d'abord: python create_test_alerts.py")
        return
    
    # Tests
    print("\n" + "="*50)
    check_viewset_method()
    
    print("\n" + "="*50)
    test_direct_assignment()
    
    print("\n" + "="*50)
    test_assignment_endpoint()
    
    print("\n🎉 Tests terminés!")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
