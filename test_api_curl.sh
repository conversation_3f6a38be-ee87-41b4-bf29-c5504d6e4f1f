#!/bin/bash

# Script pour tester l'API d'assignation avec curl

echo "🚀 Test de l'API d'assignation avec curl"

# Configuration
API_URL="http://localhost:8000/api/v1"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"

echo "📡 Obtention du token d'authentification..."

# Obtenir un token d'authentification
TOKEN_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$ADMIN_EMAIL\",\"password\":\"$ADMIN_PASSWORD\"}" \
  "$API_URL/auth/token/")

echo "🔑 Réponse d'authentification: $TOKEN_RESPONSE"

# Extraire le token (simple parsing)
TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "❌ Impossible d'obtenir le token d'authentification"
    echo "💡 Vérifiez que le serveur Django tourne et que l'admin existe"
    exit 1
fi

echo "✅ Token obtenu: ${TOKEN:0:20}..."

echo ""
echo "📊 Test de l'endpoint des agents..."

# Tester l'endpoint des agents
AGENTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/alerts/agents/")
echo "👥 Réponse agents: $AGENTS_RESPONSE"

echo ""
echo "📊 Test de l'endpoint des alertes..."

# Tester l'endpoint des alertes
ALERTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_URL/alerts/")
echo "🚨 Réponse alertes: $ALERTS_RESPONSE"

echo ""
echo "🧪 Test d'assignation..."

# Tester l'assignation (alerte ID 1, agent ID 1)
ALERT_ID=1
AGENT_ID=1

echo "🎯 Assignation de l'alerte $ALERT_ID à l'agent $AGENT_ID"

ASSIGN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
  -X PATCH \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{\"assigned_to\":$AGENT_ID,\"notes\":\"Test via curl\"}" \
  "$API_URL/alerts/$ALERT_ID/assign/")

# Séparer le status code et le body
HTTP_STATUS=$(echo $ASSIGN_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
HTTP_BODY=$(echo $ASSIGN_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

echo "📊 Status HTTP: $HTTP_STATUS"
echo "📋 Réponse: $HTTP_BODY"

if [ "$HTTP_STATUS" -eq 200 ]; then
    echo "✅ Assignation réussie!"
elif [ "$HTTP_STATUS" -eq 400 ]; then
    echo "⚠️ Erreur 400 - Requête invalide"
elif [ "$HTTP_STATUS" -eq 500 ]; then
    echo "❌ Erreur 500 - Erreur serveur interne"
    echo "💡 Vérifiez les logs Django pour plus de détails"
else
    echo "❓ Status inattendu: $HTTP_STATUS"
fi

echo ""
echo "🔍 Test avec différents IDs..."

# Tester avec d'autres IDs
for ALERT_ID in 2 3 4 5; do
    echo "🧪 Test alerte ID $ALERT_ID..."
    
    TEST_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" \
      -X PATCH \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d "{\"assigned_to\":$AGENT_ID,\"notes\":\"Test ID $ALERT_ID\"}" \
      "$API_URL/alerts/$ALERT_ID/assign/")
    
    TEST_STATUS=$(echo $TEST_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    TEST_BODY=$(echo $TEST_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')
    
    echo "  Status: $TEST_STATUS"
    if [ "$TEST_STATUS" -ne 200 ]; then
        echo "  Erreur: $TEST_BODY"
    else
        echo "  ✅ Succès"
    fi
done

echo ""
echo "🎉 Tests terminés!"
echo "💡 Si erreur 500, vérifiez les logs Django avec: python manage.py runserver"
