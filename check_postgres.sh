#!/bin/bash

# Script de vérification PostgreSQL pour Gold Sentinel v3.0

set -e

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 VÉRIFICATION POSTGRESQL - GOLD SENTINEL"
echo "=========================================="

# Charger les variables d'environnement
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    print_success "Variables d'environnement chargées"
    echo "   Base: $POSTGRES_DB"
    echo "   Host: $POSTGRES_HOST:$POSTGRES_PORT"
    echo "   User: $POSTGRES_USER"
else
    print_error "Fichier .env non trouvé"
    exit 1
fi

# Test de connexion
print_status "Test de connexion PostgreSQL..."
if PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c '\q' 2>/dev/null; then
    print_success "Connexion PostgreSQL réussie"
else
    print_error "Échec de connexion PostgreSQL"
    echo ""
    print_warning "Solutions possibles :"
    echo "1. Vérifiez que Docker PostgreSQL est démarré"
    echo "2. Vérifiez l'IP du conteneur : docker inspect <container_name> | grep IPAddress"
    echo "3. Vérifiez les paramètres dans .env"
    exit 1
fi

# Vérifier si la base existe
print_status "Vérification de l'existence de la base de données..."
if PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -lqt | cut -d \| -f 1 | grep -qw $POSTGRES_DB; then
    print_success "Base de données '$POSTGRES_DB' existe"
    
    # Vérifier les tables
    print_status "Vérification des tables Django..."
    table_count=$(PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
    
    if [ "$table_count" -gt 0 ]; then
        print_success "$table_count tables trouvées"
        
        # Vérifier les utilisateurs
        print_status "Vérification des utilisateurs..."
        if PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user');" | grep -q t; then
            user_count=$(PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT COUNT(*) FROM \"user\";" | xargs)
            print_success "$user_count utilisateurs dans la base"
            
            # Lister les utilisateurs
            if [ "$user_count" -gt 0 ]; then
                echo ""
                echo "👥 UTILISATEURS EXISTANTS :"
                PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT email, first_name, last_name, is_superuser, is_staff FROM \"user\";"
            fi
        else
            print_warning "Table 'user' non trouvée - migrations nécessaires"
        fi
    else
        print_warning "Aucune table trouvée - migrations nécessaires"
    fi
else
    print_warning "Base de données '$POSTGRES_DB' n'existe pas"
    echo ""
    print_status "Création de la base de données..."
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c "CREATE DATABASE $POSTGRES_DB;"
    print_success "Base de données créée"
fi

# Vérifier les migrations Django
print_status "Vérification des migrations Django..."
if [ -d "venv" ]; then
    source venv/bin/activate
    
    # Test de connexion Django
    if python manage.py check --database default 2>/dev/null; then
        print_success "Configuration Django OK"
        
        # Vérifier les migrations en attente
        pending_migrations=$(python manage.py showmigrations --plan | grep '\[ \]' | wc -l)
        if [ "$pending_migrations" -gt 0 ]; then
            print_warning "$pending_migrations migrations en attente"
            echo ""
            echo "🔧 ACTIONS RECOMMANDÉES :"
            echo "1. Appliquer les migrations : python manage.py migrate"
            echo "2. Ou réinitialiser la base : ./reset_database.sh"
        else
            print_success "Toutes les migrations sont appliquées"
        fi
    else
        print_error "Problème de configuration Django"
    fi
else
    print_warning "Environnement virtuel non trouvé"
fi

echo ""
print_success "🎯 VÉRIFICATION TERMINÉE"

# Résumé des actions possibles
echo ""
echo "🛠️  ACTIONS DISPONIBLES :"
echo "========================"
echo "• Réinitialiser la base : ./reset_database.sh"
echo "• Démarrer l'application : ./start_gold_sentinel.sh"
echo "• Vérifier les migrations : source venv/bin/activate && python manage.py showmigrations"
