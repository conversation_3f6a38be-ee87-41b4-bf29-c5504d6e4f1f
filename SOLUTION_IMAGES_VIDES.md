# 🖼️ Solution : Base de Données Images Vide

## 🎯 **Problème Résolu**

✅ **L'API fonctionne correctement** (status 200)  
❌ **La base de données est vide** (count: 0)

## 🚀 **Solution Rapide**

### **Option 1 : Commande Django (Recommandée)**
```bash
# Dans le répertoire backend
cd /path/to/backend
python manage.py create_test_images
```

### **Option 2 : Script Python**
```bash
# Dans le répertoire racine du projet
python create_test_images.py
```

### **Option 3 : Création manuelle via Django Shell**
```bash
python manage.py shell
```

<PERSON>uis dans le shell Python :
```python
from image.models.image_model import ImageModel
from region.models.region_model import RegionModel
from datetime import date

# Créer une région si elle n'existe pas
region, created = RegionModel.objects.get_or_create(
    name='BONDOUKOU',
    defaults={'code': 'BDK'}
)

# Créer une image de test
ImageModel.objects.create(
    name="Image Test Bondoukou",
    capture_date=date.today(),
    satellite_source="SENTINEL_2",
    cloud_coverage=15.0,
    resolution=10.0,
    gee_asset_id="test_asset_bondoukou_1",
    gee_collection="COPERNICUS/S2_SR",
    processing_status="COMPLETED",
    region=region,
    center_lat=8.0402,
    center_lon=-2.8000
)

print("Image créée avec succès!")
```

## 📊 **Vérification**

Après avoir créé les images, vérifiez :

1. **Dans Django Shell :**
```python
from image.models.image_model import ImageModel
print(f"Nombre d'images: {ImageModel.objects.count()}")
```

2. **Via l'API directement :**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/v1/images/
```

3. **Dans le frontend :**
   - Actualisez la page Images
   - Cliquez sur "Test API" (mode dev)
   - Vérifiez la console pour `count > 0`

## 🎉 **Résultat Attendu**

Après exécution, vous devriez voir :
- **20 images de test** (5 par région)
- **4 régions** : BONDOUKOU, BOUNA, TANDA, NASSIAN
- **Différents statuts** : COMPLETED, PROCESSING, FAILED
- **Différents satellites** : SENTINEL_2, LANDSAT_8, SENTINEL_1

## 🔧 **Commandes Utiles**

### **Supprimer les images de test**
```bash
python manage.py create_test_images --clear
```

### **Créer plus d'images**
```bash
python manage.py create_test_images --count 10
```

### **Vérifier la base de données**
```bash
python manage.py shell -c "
from image.models.image_model import ImageModel
print('Images:', ImageModel.objects.count())
for img in ImageModel.objects.all()[:5]:
    print(f'- {img.name} ({img.region.name})')
"
```

## 🐛 **Si le problème persiste**

1. **Vérifiez les migrations :**
```bash
python manage.py makemigrations
python manage.py migrate
```

2. **Vérifiez les permissions de base de données**

3. **Redémarrez le serveur Django :**
```bash
python manage.py runserver 0.0.0.0:8000
```

4. **Actualisez le frontend**

---

**✅ Une fois les images créées, la page Images devrait afficher les données correctement !**
