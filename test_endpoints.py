#!/usr/bin/env python3
"""
Test simple des endpoints d'alertes
"""
import requests

BASE_URL = "http://localhost:8000/api/v1"

def test_endpoints():
    """Test des endpoints sans authentification"""
    
    endpoints = [
        "/alerts/",
        "/alerts/active/", 
        "/alerts/critical/",
        "/investigations/available-agents/",
        "/stats/dashboard/",
    ]
    
    print("🔍 Test des endpoints (sans authentification):")
    print("=" * 50)
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            status = "✅ OK" if response.status_code == 401 else f"❌ {response.status_code}"
            print(f"{endpoint:<35} {status}")
        except Exception as e:
            print(f"{endpoint:<35} ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("Note: Status 401 = Authentification requise (normal)")
    print("Autres status = Problème avec l'endpoint")

if __name__ == "__main__":
    test_endpoints()
