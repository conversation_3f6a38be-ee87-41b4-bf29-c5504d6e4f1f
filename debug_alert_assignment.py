#!/usr/bin/env python3
"""
Script de debug pour l'assignation d'alertes
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gold_mining_detection.settings')
django.setup()

from alert.models.alert_model import AlertModel
from account.models.user_model import UserModel

def debug_agents():
    """Debug des agents"""
    print("🔍 DEBUG: Agents dans la base de données")
    
    all_users = UserModel.objects.all()
    print(f"Total utilisateurs: {all_users.count()}")
    
    for user in all_users:
        print(f"  - {user.email}: role='{user.role}', active={user.is_active}")
    
    agents = UserModel.objects.filter(role='AGENT', is_active=True)
    print(f"\nAgents avec role='AGENT': {agents.count()}")
    
    return agents

def debug_alerts():
    """Debug des alertes"""
    print("\n🔍 DEBUG: Alertes dans la base de données")
    
    all_alerts = AlertModel.objects.all()
    print(f"Total alertes: {all_alerts.count()}")
    
    for alert in all_alerts:
        assigned_to = alert.assigned_to.get_full_name() if alert.assigned_to else "Non assignée"
        print(f"  - ID {alert.id}: {alert.title} | Statut: {alert.alert_status} | Assignée à: {assigned_to}")
    
    return all_alerts

def debug_workload():
    """Debug de la charge de travail"""
    print("\n🔍 DEBUG: Calcul de charge de travail")
    
    agents = UserModel.objects.filter(role='AGENT', is_active=True)
    
    for agent in agents:
        print(f"\n👤 Agent: {agent.get_full_name()}")
        
        # Toutes les alertes assignées à cet agent
        all_assigned = AlertModel.objects.filter(assigned_to=agent)
        print(f"  - Total alertes assignées: {all_assigned.count()}")
        
        # Alertes actives
        active_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
        )
        print(f"  - Alertes actives/accusées: {active_alerts.count()}")
        
        # Alertes résolues
        resolved_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status='RESOLVED'
        )
        print(f"  - Alertes résolues: {resolved_alerts.count()}")
        
        # Détail des alertes
        for alert in all_assigned:
            print(f"    * ID {alert.id}: {alert.alert_status}")

def test_assignment():
    """Test d'assignation manuelle"""
    print("\n🧪 TEST: Assignation manuelle")
    
    # Récupérer un agent
    agent = UserModel.objects.filter(role='AGENT', is_active=True).first()
    if not agent:
        print("❌ Aucun agent trouvé")
        return
    
    # Récupérer une alerte non assignée
    alert = AlertModel.objects.filter(assigned_to__isnull=True).first()
    if not alert:
        print("❌ Aucune alerte non assignée trouvée")
        return
    
    print(f"Assignation de l'alerte {alert.id} à {agent.get_full_name()}")
    
    # Charge avant
    before = AlertModel.objects.filter(
        assigned_to=agent,
        alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
    ).count()
    print(f"Charge avant: {before}")
    
    # Assigner
    alert.assigned_to = agent
    alert.alert_status = 'ACKNOWLEDGED'
    alert.save()
    
    # Charge après
    after = AlertModel.objects.filter(
        assigned_to=agent,
        alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
    ).count()
    print(f"Charge après: {after}")
    
    print("✅ Assignation terminée")

if __name__ == '__main__':
    print("🚀 DEBUG du système d'assignation d'alertes\n")
    
    agents = debug_agents()
    alerts = debug_alerts()
    debug_workload()
    
    if agents.count() > 0 and alerts.count() > 0:
        test_assignment()
        print("\n📊 Charge après test:")
        debug_workload()
    
    print("\n🎯 Résumé:")
    print(f"  - Agents: {agents.count()}")
    print(f"  - Alertes: {alerts.count()}")
    print("\n💡 Testez maintenant l'API avec curl ou le frontend")
