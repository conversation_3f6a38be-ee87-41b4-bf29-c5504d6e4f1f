#!/usr/bin/env python3
"""
VÉRIFICATION FINALE COMPLÈTE - GOLD SENTINEL v3.0
Teste absolument tous les aspects du système
"""

import os
import sys
import requests
import json
import subprocess
from pathlib import Path

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5173"
API_BASE = f"{BACKEND_URL}/api/v1"

def print_header(title):
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_status(message):
    print(f"📋 {message}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def check_file_exists(filepath, description):
    """Vérifie qu'un fichier existe"""
    if Path(filepath).exists():
        print_success(f"{description} existe")
        return True
    else:
        print_error(f"{description} manquant: {filepath}")
        return False

def check_directory_structure():
    """Vérifie la structure des dossiers"""
    print_header("STRUCTURE DES DOSSIERS")
    
    required_files = [
        ("frontend/package.json", "Package.json frontend"),
        ("frontend/src/main.tsx", "Point d'entrée React"),
        ("frontend/src/services/auth.service.ts", "Service d'authentification"),
        ("frontend/src/services/analysis.service.ts", "Service d'analyse"),
        ("frontend/.env.development", "Configuration développement"),
        ("requirements.txt", "Dépendances Python"),
        ("config/settings.py", "Configuration Django"),
        ("api/urls.py", "URLs API"),
        ("api/viewsets/auth_viewsets.py", "ViewSet authentification"),
        ("start_gold_sentinel.sh", "Script de démarrage"),
        ("test_final_verification.py", "Script de test"),
    ]
    
    all_exist = True
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            all_exist = False
    
    return all_exist

def check_backend_configuration():
    """Vérifie la configuration backend"""
    print_header("CONFIGURATION BACKEND")
    
    try:
        # Vérifier settings.py
        with open('config/settings.py', 'r') as f:
            settings_content = f.read()
        
        checks = [
            ('localhost:5173', 'CORS pour Vite'),
            ('rest_framework_simplejwt', 'JWT configuré'),
            ('corsheaders', 'CORS headers'),
            ("AUTH_USER_MODEL = 'account.UserModel'", 'Modèle utilisateur custom'),
        ]
        
        for check, description in checks:
            if check in settings_content:
                print_success(f"{description}")
            else:
                print_error(f"{description} manquant")
                
        return True
    except Exception as e:
        print_error(f"Erreur lecture configuration: {e}")
        return False

def check_frontend_configuration():
    """Vérifie la configuration frontend"""
    print_header("CONFIGURATION FRONTEND")
    
    try:
        # Vérifier .env.development
        with open('frontend/.env.development', 'r') as f:
            env_content = f.read()
        
        checks = [
            ('VITE_API_URL=http://localhost:8000/api/v1', 'URL API correcte'),
            ('VITE_APP_NAME=Gold Sentinel', 'Nom application'),
        ]
        
        for check, description in checks:
            if check in env_content:
                print_success(f"{description}")
            else:
                print_error(f"{description} manquant")
        
        # Vérifier package.json
        with open('frontend/package.json', 'r') as f:
            package_data = json.load(f)
        
        required_deps = ['react', 'axios', 'react-router-dom', '@tanstack/react-query']
        for dep in required_deps:
            if dep in package_data.get('dependencies', {}):
                print_success(f"Dépendance {dep} présente")
            else:
                print_error(f"Dépendance {dep} manquante")
                
        return True
    except Exception as e:
        print_error(f"Erreur lecture configuration frontend: {e}")
        return False

def check_services_alignment():
    """Vérifie l'alignement des services"""
    print_header("ALIGNEMENT SERVICES FRONTEND/BACKEND")
    
    try:
        # Vérifier auth.service.ts
        with open('frontend/src/services/auth.service.ts', 'r') as f:
            auth_content = f.read()
        
        correct_endpoints = [
            ('/auth/token/', 'Endpoint login JWT'),
            ('/auth/token/refresh/', 'Endpoint refresh JWT'),
            ('/auth/profile/', 'Endpoint profil'),
            ('/auth/change-password/', 'Endpoint changement mot de passe'),
        ]
        
        for endpoint, description in correct_endpoints:
            if endpoint in auth_content:
                print_success(f"{description} correct")
            else:
                print_error(f"{description} incorrect")
        
        # Vérifier permissions
        if 'canLaunchAnalysis' in auth_content and "'Agent Analyste'" not in auth_content.split('canLaunchAnalysis')[1].split('}')[0]:
            print_success("Permissions analyse correctes (pas Agent Analyste)")
        else:
            print_error("Permissions analyse incorrectes")
            
        return True
    except Exception as e:
        print_error(f"Erreur vérification services: {e}")
        return False

def test_backend_endpoints():
    """Teste les endpoints backend"""
    print_header("TEST ENDPOINTS BACKEND")
    
    try:
        # Test santé
        response = requests.get(f"{API_BASE}/", timeout=5)
        if response.status_code == 200:
            print_success("Backend accessible")
        else:
            print_error(f"Backend inaccessible: {response.status_code}")
            return False
        
        # Test login
        login_data = {"email": "<EMAIL>", "password": "admin123"}
        auth_response = requests.post(f"{API_BASE}/auth/token/", json=login_data, timeout=10)
        
        if auth_response.status_code == 200:
            print_success("Endpoint login fonctionnel")
            token = auth_response.json()['access']
            headers = {'Authorization': f'Bearer {token}'}
            
            # Test profil
            profile_response = requests.get(f"{API_BASE}/auth/profile/", headers=headers, timeout=5)
            if profile_response.status_code == 200:
                print_success("Endpoint profil fonctionnel")
                profile = profile_response.json()
                print(f"   Utilisateur: {profile.get('email')}")
                print(f"   Autorités: {profile.get('authorities')}")
            else:
                print_error("Endpoint profil défaillant")
            
            # Test autres endpoints
            endpoints = [
                '/stats/dashboard/',
                '/images/',
                '/detections/',
                '/alerts/',
                '/investigations/',
                '/financial-risks/',
            ]
            
            for endpoint in endpoints:
                try:
                    resp = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=5)
                    if resp.status_code in [200, 404]:
                        print_success(f"Endpoint {endpoint} accessible")
                    else:
                        print_error(f"Endpoint {endpoint} problème: {resp.status_code}")
                except Exception as e:
                    print_error(f"Endpoint {endpoint} erreur: {e}")
            
            return True
        else:
            print_error(f"Login échoue: {auth_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erreur test backend: {e}")
        return False

def check_scripts_executable():
    """Vérifie que les scripts sont exécutables"""
    print_header("SCRIPTS EXÉCUTABLES")
    
    scripts = [
        'start_gold_sentinel.sh',
        'test_final_verification.py',
        'verification_finale_complete.py',
    ]
    
    all_executable = True
    for script in scripts:
        if Path(script).exists():
            if os.access(script, os.X_OK):
                print_success(f"{script} exécutable")
            else:
                print_warning(f"{script} pas exécutable - correction...")
                os.chmod(script, 0o755)
                print_success(f"{script} rendu exécutable")
        else:
            print_error(f"{script} n'existe pas")
            all_executable = False
    
    return all_executable

def main():
    """Fonction principale"""
    print("🚀 VÉRIFICATION FINALE COMPLÈTE - GOLD SENTINEL v3.0")
    print("=" * 60)
    
    tests = [
        ("Structure des dossiers", check_directory_structure),
        ("Configuration backend", check_backend_configuration),
        ("Configuration frontend", check_frontend_configuration),
        ("Alignement services", check_services_alignment),
        ("Scripts exécutables", check_scripts_executable),
        ("Endpoints backend", test_backend_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print_error(f"Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé final
    print_header("RÉSUMÉ FINAL")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSÉ" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Résultat global: {passed}/{total} tests passés")
    
    if passed == total:
        print_success("🎉 SYSTÈME 100% OPÉRATIONNEL !")
        print("\n🚀 PRÊT POUR UTILISATION:")
        print("1. ./start_gold_sentinel.sh")
        print("2. http://localhost:5173")
        print("3. <EMAIL> / admin123")
        return 0
    else:
        print_error("❌ PROBLÈMES DÉTECTÉS")
        print("Corrigez les erreurs ci-dessus avant utilisation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
