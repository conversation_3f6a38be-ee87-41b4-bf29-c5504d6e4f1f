#!/usr/bin/env python3
"""
Script pour créer des alertes et agents de test dans la base de données Django
Exécuter depuis le répertoire racine du projet Django :
python create_test_alerts.py
"""

import os
import sys
import django
from datetime import datetime, timedelta
from decimal import Decimal

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gold_mining_detection.settings')
django.setup()

from alert.models.alert_model import AlertModel
from region.models.region_model import RegionModel
from account.models.user_model import UserModel
from detection.models.detection_model import DetectionModel

def create_test_agents():
    """Créer des agents de test"""
    print("👥 Création des agents de test...")
    
    agents_data = [
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON><PERSON>',
            'role': 'AGENT'
        },
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON><PERSON>',
            'role': 'AGENT'
        },
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': 'Ouattara',
            'role': 'AGENT'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Fatou',
            'last_name': 'Traoré',
            'role': 'AGENT'
        }
    ]
    
    agents_created = 0
    for agent_data in agents_data:
        agent, created = UserModel.objects.get_or_create(
            email=agent_data['email'],
            defaults={
                'first_name': agent_data['first_name'],
                'last_name': agent_data['last_name'],
                'role': agent_data['role'],
                'is_active': True,
                'is_staff': False,
                'is_superuser': False
            }
        )
        
        if created:
            agent.set_password('agent123')  # Mot de passe par défaut
            agent.save()
            agents_created += 1
            print(f"✅ Agent créé: {agent.get_full_name()} ({agent.email})")
        else:
            print(f"ℹ️ Agent existe déjà: {agent.get_full_name()}")
    
    return agents_created

def create_test_alerts():
    """Créer des alertes de test"""
    print("🚨 Création des alertes de test...")
    
    # Vérifier les régions
    regions = RegionModel.objects.all()
    if not regions.exists():
        print("❌ Aucune région trouvée. Création des régions...")
        regions_data = [
            {'name': 'BONDOUKOU', 'code': 'BDK'},
            {'name': 'BOUNA', 'code': 'BNA'},
            {'name': 'TANDA', 'code': 'TDA'},
            {'name': 'NASSIAN', 'code': 'NSN'},
        ]
        
        for region_data in regions_data:
            region, created = RegionModel.objects.get_or_create(
                name=region_data['name'],
                defaults={'code': region_data['code']}
            )
            if created:
                print(f"✅ Région créée: {region.name}")
        
        regions = RegionModel.objects.all()
    
    # Récupérer les agents
    agents = list(UserModel.objects.filter(role='AGENT', is_active=True))
    if not agents:
        print("❌ Aucun agent trouvé. Créez d'abord des agents.")
        return 0
    
    # Supprimer les anciennes alertes de test
    test_alerts = AlertModel.objects.filter(title__startswith='Test Alert')
    if test_alerts.exists():
        count = test_alerts.count()
        test_alerts.delete()
        print(f"🗑️ {count} ancienne(s) alerte(s) de test supprimée(s)")
    
    # Créer des alertes de test
    alert_types = ['MINING_DETECTION', 'ENVIRONMENTAL_DAMAGE', 'ILLEGAL_ACTIVITY']
    levels = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']
    statuses = ['ACTIVE', 'ACKNOWLEDGED', 'RESOLVED']
    
    alerts_created = 0
    
    for i, region in enumerate(regions):
        for j in range(3):  # 3 alertes par région
            sent_at = datetime.now() - timedelta(hours=j*2)  # Alertes espacées de 2h
            
            # Assigner aléatoirement à des agents (certaines non assignées)
            assigned_to = agents[j % len(agents)] if j < 2 else None
            
            alert = AlertModel.objects.create(
                title=f"Test Alert {region.name} #{j+1}",
                message=f"Détection d'activité suspecte dans la région {region.name}. "
                       f"Coordonnées: {8.0 + i*0.1:.4f}, {-3.0 + i*0.1:.4f}",
                level=levels[j % len(levels)],
                alert_type=alert_types[j % len(alert_types)],
                alert_status=statuses[0] if not assigned_to else statuses[j % len(statuses)],
                region=region,
                latitude=8.0 + i*0.1 + j*0.01,
                longitude=-3.0 + i*0.1 + j*0.01,
                sent_at=sent_at,
                assigned_to=assigned_to,
                is_read=assigned_to is not None,  # Lue si assignée
                metadata={
                    'detection_confidence': 0.85 + j*0.05,
                    'area_affected_hectares': 2.5 + j*0.5,
                    'estimated_damage': f"{1000 + j*500} USD"
                }
            )
            
            alerts_created += 1
            status_info = f"({alert.alert_status}"
            if assigned_to:
                status_info += f", assignée à {assigned_to.get_full_name()}"
            status_info += ")"
            
            print(f"✅ Alerte créée: {alert.title} {status_info}")
    
    return alerts_created

def verify_data():
    """Vérifier les données créées"""
    print("\n🔍 Vérification des données...")
    
    # Statistiques agents
    agents = UserModel.objects.filter(role='AGENT', is_active=True)
    print(f"👥 Agents: {agents.count()}")
    
    for agent in agents:
        current_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
        ).count()
        resolved_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status='RESOLVED'
        ).count()
        print(f"  - {agent.get_full_name()}: {current_alerts} actives, {resolved_alerts} résolues")
    
    # Statistiques alertes
    total_alerts = AlertModel.objects.count()
    active_alerts = AlertModel.objects.filter(alert_status='ACTIVE').count()
    assigned_alerts = AlertModel.objects.filter(assigned_to__isnull=False).count()
    
    print(f"\n🚨 Alertes: {total_alerts} total")
    print(f"  - Actives: {active_alerts}")
    print(f"  - Assignées: {assigned_alerts}")
    
    # Par région
    print(f"\n📍 Par région:")
    regions = RegionModel.objects.all()
    for region in regions:
        count = AlertModel.objects.filter(region=region).count()
        print(f"  - {region.name}: {count} alerte(s)")

if __name__ == '__main__':
    try:
        print("🚀 Création des données de test pour les alertes...")
        
        agents_created = create_test_agents()
        alerts_created = create_test_alerts()
        
        print(f"\n🎉 Données créées avec succès!")
        print(f"  - {agents_created} nouveaux agents")
        print(f"  - {alerts_created} nouvelles alertes")
        
        verify_data()
        
        print("\n💡 Vous pouvez maintenant:")
        print("  1. Tester l'assignation d'alertes dans le frontend")
        print("  2. Voir les alertes par région sur la carte")
        print("  3. Vérifier la charge de travail des agents")
        
        print("\n🔑 Connexions agents:")
        print("  - <EMAIL> / agent123")
        print("  - <EMAIL> / agent123")
        print("  - <EMAIL> / agent123")
        print("  - <EMAIL> / agent123")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de l'exécution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
