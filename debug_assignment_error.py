#!/usr/bin/env python3
"""
Script de debug pour identifier l'erreur 500 d'assignation
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gold_mining_detection.settings')
django.setup()

from alert.models.alert_model import AlertModel
from account.models.user_model import UserModel
from api.serializers.alert_serializer import AlertSerializer

def test_serializer():
    """Tester le serializer d'alertes"""
    print("🔍 Test du serializer AlertSerializer...")
    
    try:
        # Récupérer une alerte
        alert = AlertModel.objects.first()
        if not alert:
            print("❌ Aucune alerte trouvée")
            return False
        
        print(f"📋 Alerte trouvée: {alert.title}")
        
        # Tester la sérialisation
        serializer = AlertSerializer(alert)
        data = serializer.data
        
        print(f"✅ Sérialisation réussie")
        print(f"📊 Données sérialisées: {list(data.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de sérialisation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assignment_logic():
    """Tester la logique d'assignation étape par étape"""
    print("\n🧪 Test de la logique d'assignation...")
    
    try:
        # Étape 1: Récupérer une alerte non assignée
        alert = AlertModel.objects.filter(assigned_to__isnull=True).first()
        if not alert:
            print("❌ Aucune alerte non assignée trouvée")
            return False
        
        print(f"✅ Étape 1: Alerte trouvée - ID {alert.id}")
        
        # Étape 2: Récupérer un agent
        agent = UserModel.objects.filter(role='AGENT', is_active=True).first()
        if not agent:
            print("❌ Aucun agent trouvé")
            return False
        
        print(f"✅ Étape 2: Agent trouvé - {agent.get_full_name()}")
        
        # Étape 3: Vérifier que l'alerte n'est pas déjà assignée
        if alert.assigned_to is not None:
            print(f"❌ Étape 3: Alerte déjà assignée à {alert.assigned_to.get_full_name()}")
            return False
        
        print(f"✅ Étape 3: Alerte non assignée")
        
        # Étape 4: Calculer la charge de travail
        current_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
        ).count()
        
        print(f"✅ Étape 4: Charge actuelle de l'agent: {current_alerts}")
        
        # Étape 5: Assigner l'alerte
        alert.assigned_to = agent
        alert.alert_status = 'ACKNOWLEDGED'
        alert.is_read = True
        alert.save()
        
        print(f"✅ Étape 5: Assignation sauvegardée")
        
        # Étape 6: Tester la sérialisation après assignation
        serializer = AlertSerializer(alert)
        data = serializer.data
        
        print(f"✅ Étape 6: Sérialisation après assignation réussie")
        
        # Étape 7: Vérifier la nouvelle charge
        new_current_alerts = AlertModel.objects.filter(
            assigned_to=agent,
            alert_status__in=['ACTIVE', 'ACKNOWLEDGED']
        ).count()
        
        print(f"✅ Étape 7: Nouvelle charge: {new_current_alerts}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans la logique d'assignation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_viewset_method():
    """Tester la méthode du ViewSet directement"""
    print("\n🔧 Test de la méthode assign_alert du ViewSet...")
    
    try:
        from api.viewsets.alert_viewsets import AlertViewSet
        from rest_framework.test import APIRequestFactory
        from django.contrib.auth.models import AnonymousUser
        
        # Créer une requête factice
        factory = APIRequestFactory()
        
        # Récupérer une alerte et un agent
        alert = AlertModel.objects.filter(assigned_to__isnull=True).first()
        agent = UserModel.objects.filter(role='AGENT', is_active=True).first()
        
        if not alert or not agent:
            print("❌ Données insuffisantes pour le test")
            return False
        
        # Créer la requête PATCH
        request_data = {
            'assigned_to': agent.id,
            'notes': 'Test direct du ViewSet'
        }
        
        request = factory.patch(f'/api/v1/alerts/{alert.id}/assign/', request_data, format='json')
        request.user = agent  # Simuler un utilisateur authentifié
        
        # Créer une instance du ViewSet
        viewset = AlertViewSet()
        viewset.request = request
        viewset.format_kwarg = None
        
        # Simuler get_object()
        viewset.kwargs = {'pk': alert.id}
        
        print(f"🎯 Test avec alerte ID {alert.id} et agent {agent.get_full_name()}")
        
        # Appeler la méthode
        response = viewset.assign_alert(request, pk=alert.id)
        
        print(f"📊 Réponse du ViewSet:")
        print(f"  - Status: {response.status_code}")
        print(f"  - Data: {response.data}")
        
        if response.status_code == 200:
            print("✅ ViewSet fonctionne correctement")
            return True
        else:
            print(f"❌ ViewSet retourne une erreur: {response.data}")
            return False
        
    except Exception as e:
        print(f"❌ Erreur dans le test du ViewSet: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de debug"""
    print("🚀 Debug de l'erreur 500 d'assignation\n")
    
    # Vérifier les données de base
    agents = UserModel.objects.filter(role='AGENT', is_active=True)
    alerts = AlertModel.objects.filter(assigned_to__isnull=True)
    
    print(f"📊 État des données:")
    print(f"  - Agents: {agents.count()}")
    print(f"  - Alertes non assignées: {alerts.count()}")
    
    if agents.count() == 0 or alerts.count() == 0:
        print("\n❌ Données insuffisantes")
        print("💡 Exécutez: python create_test_alerts.py")
        return
    
    # Tests progressifs
    print("\n" + "="*50)
    if not test_serializer():
        print("❌ Échec du test de sérialisation")
        return
    
    print("\n" + "="*50)
    if not test_assignment_logic():
        print("❌ Échec du test de logique d'assignation")
        return
    
    print("\n" + "="*50)
    if not test_viewset_method():
        print("❌ Échec du test du ViewSet")
        return
    
    print("\n🎉 Tous les tests passent!")
    print("💡 L'erreur 500 pourrait venir d'un problème de permissions ou de configuration")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"\n❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
