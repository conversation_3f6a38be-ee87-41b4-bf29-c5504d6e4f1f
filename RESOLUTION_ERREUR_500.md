# 🚨 Résolution Erreur 500 - Assignation d'Alertes

## 🎯 **Problème**
Erreur 500 (Internal Server Error) lors de l'assignation d'alertes via PATCH `/api/v1/alerts/{id}/assign/`

## 🔍 **Diagnostic Étape par Étape**

### **Étape 1 : Vérifier les Données de Base**
```bash
python debug_assignment_error.py
```

**Vérifications :**
- [ ] Au moins 1 agent avec `role='AGENT'`
- [ ] Au moins 1 alerte non assignée
- [ ] Serializer AlertSerializer fonctionne
- [ ] Logique d'assignation directe fonctionne

### **Étape 2 : Tester l'API Directement**
```bash
chmod +x test_api_curl.sh
./test_api_curl.sh
```

**Ou manuellement :**
```bash
# Obtenir un token
curl -X POST -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"admin123"}' \
     http://localhost:8000/api/v1/auth/token/

# Tester l'assignation
curl -X PATCH -H "Authorization: Bearer TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"assigned_to":1,"notes":"Test"}' \
     http://localhost:8000/api/v1/alerts/1/assign/
```

### **Étape 3 : Vérifier les Logs Django**
```bash
# Dans le terminal où tourne Django
python manage.py runserver 0.0.0.0:8000
```

**Rechercher dans les logs :**
- Messages d'erreur Python
- Traces de stack
- Erreurs de base de données

## 🛠️ **Causes Possibles et Solutions**

### **Cause 1 : Données Manquantes**
**Symptômes :** Aucun agent ou alerte trouvé
**Solution :**
```bash
python create_test_alerts.py
```

### **Cause 2 : Problème de Serializer**
**Symptômes :** Erreur lors de `serializer.data`
**Solution :** Vérifier `api/serializers/alert_serializer.py`

### **Cause 3 : Problème de Permissions**
**Symptômes :** Erreur d'accès ou de permissions
**Solution :** Vérifier les permissions dans `AlertViewSet`

### **Cause 4 : Problème de Base de Données**
**Symptômes :** Erreur lors de `alert.save()`
**Solution :**
```bash
python manage.py makemigrations
python manage.py migrate
```

### **Cause 5 : Problème de Modèle**
**Symptômes :** Erreur lors de l'accès aux champs
**Solution :** Vérifier `alert.models.alert_model.py`

## 🧪 **Tests de Validation**

### **Test 1 : Données de Base**
```python
# Dans Django shell
from alert.models.alert_model import AlertModel
from account.models.user_model import UserModel

agents = UserModel.objects.filter(role='AGENT', is_active=True)
alerts = AlertModel.objects.filter(assigned_to__isnull=True)
print(f"Agents: {agents.count()}, Alertes: {alerts.count()}")
```

### **Test 2 : Assignation Directe**
```python
# Dans Django shell
alert = AlertModel.objects.filter(assigned_to__isnull=True).first()
agent = UserModel.objects.filter(role='AGENT').first()

alert.assigned_to = agent
alert.alert_status = 'ACKNOWLEDGED'
alert.save()
print("Assignation directe réussie")
```

### **Test 3 : Serializer**
```python
# Dans Django shell
from api.serializers.alert_serializer import AlertSerializer
alert = AlertModel.objects.first()
serializer = AlertSerializer(alert)
print(serializer.data)
```

## 🚨 **Messages d'Erreur Courants**

### **"Agent introuvable ou inactif"**
- Vérifier que l'agent existe avec `role='AGENT'`
- Vérifier que l'agent est actif (`is_active=True`)

### **"Alerte déjà assignée"**
- Vérifier que l'alerte n'est pas déjà assignée
- Utiliser une autre alerte pour le test

### **"Erreur de sérialisation"**
- Vérifier le serializer AlertSerializer
- Vérifier les champs du modèle AlertModel

### **"Erreur de base de données"**
- Vérifier les migrations
- Vérifier la connexion à la base de données

## 📞 **Support Avancé**

Si l'erreur persiste après ces étapes :

1. **Capturer l'erreur exacte :**
   ```bash
   python debug_assignment_error.py > debug_output.txt 2>&1
   ```

2. **Vérifier la configuration Django :**
   - `settings.py` - Configuration de la base de données
   - `urls.py` - Routing des endpoints
   - Permissions et authentification

3. **Tester en mode debug :**
   ```python
   # Dans settings.py
   DEBUG = True
   ```

4. **Vérifier les dépendances :**
   ```bash
   pip list | grep -E "(django|rest)"
   ```

## 🎯 **Résolution Rapide**

**Si vous êtes pressé :**

1. **Redémarrer Django :** `Ctrl+C` puis `python manage.py runserver`
2. **Créer des données :** `python create_test_alerts.py`
3. **Tester avec curl :** `./test_api_curl.sh`
4. **Vérifier les logs** pour l'erreur exacte

---

**🎯 Objectif :** Identifier et corriger l'erreur 500 pour permettre l'assignation d'alertes.
