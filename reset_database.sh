#!/bin/bash

# Script de réinitialisation de la base de données PostgreSQL
# Pour Gold Sentinel v3.0

set -e

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔄 RÉINITIALISATION BASE DE DONNÉES POSTGRESQL"
echo "=============================================="

# Charger les variables d'environnement
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    print_success "Variables d'environnement chargées"
else
    print_error "Fichier .env non trouvé"
    exit 1
fi

# Vérifier que PostgreSQL est accessible
print_status "Vérification de la connexion PostgreSQL..."
if ! PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c '\q' 2>/dev/null; then
    print_error "Impossible de se connecter à PostgreSQL"
    print_warning "Vérifiez que votre conteneur Docker PostgreSQL est démarré"
    print_warning "Et que les paramètres de connexion dans .env sont corrects"
    exit 1
fi
print_success "Connexion PostgreSQL OK"

# Demander confirmation
echo ""
print_warning "⚠️  ATTENTION : Cette opération va SUPPRIMER toutes les données existantes !"
print_warning "Base de données : $POSTGRES_DB"
print_warning "Serveur : $POSTGRES_HOST:$POSTGRES_PORT"
echo ""
read -p "Êtes-vous sûr de vouloir continuer ? (oui/non): " confirm

if [ "$confirm" != "oui" ]; then
    print_status "Opération annulée"
    exit 0
fi

# Supprimer et recréer la base de données
print_status "Suppression de la base de données existante..."
PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c "DROP DATABASE IF EXISTS $POSTGRES_DB;"
print_success "Base de données supprimée"

print_status "Création de la nouvelle base de données..."
PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d postgres -c "CREATE DATABASE $POSTGRES_DB;"
print_success "Nouvelle base de données créée"

# Activer l'environnement virtuel et appliquer les migrations
print_status "Activation de l'environnement virtuel..."
source venv/bin/activate
print_success "Environnement virtuel activé"

print_status "Application des migrations Django..."
python manage.py migrate
print_success "Migrations appliquées"

# Charger les données initiales si elles existent
if [ -f "initial_data.json" ]; then
    print_status "Chargement des données initiales..."
    python manage.py loaddata initial_data.json
    print_success "Données initiales chargées"
else
    print_warning "Fichier initial_data.json non trouvé - création manuelle des utilisateurs nécessaire"
fi

# Créer le superutilisateur
print_status "Création <NAME_EMAIL>..."
python manage.py shell -c "
from account.models.user_model import UserModel
from account.models.authority_model import AuthorityModel
from account.models.user_authority_model import UserAuthorityModel

# Créer ou récupérer l'utilisateur admin
admin_user, created = UserModel.objects.get_or_create(
    email='<EMAIL>',
    defaults={
        'first_name': 'Admin',
        'last_name': 'Gold Sentinel',
        'is_staff': True,
        'is_superuser': True,
        'job_title': 'Administrateur Système',
        'institution': 'Gold Sentinel',
        'authorized_region': 'BONDOUKOU'
    }
)

if created:
    admin_user.set_password('admin123')
    admin_user.save()
    print('✅ Utilisateur admin créé')
else:
    print('ℹ️  Utilisateur admin existe déjà')

# Créer l'autorité Administrateur si elle n'existe pas
admin_authority, created = AuthorityModel.objects.get_or_create(
    name='Administrateur',
    defaults={'description': 'Accès complet au système'}
)

if created:
    print('✅ Autorité Administrateur créée')

# Assigner l'autorité à l'utilisateur
user_authority, created = UserAuthorityModel.objects.get_or_create(
    user=admin_user,
    authority=admin_authority,
    defaults={'is_primary': True, 'status': True}
)

if created:
    print('✅ Autorité assignée à l\'admin')

print('🎉 Configuration terminée !')
"

print_success "Superutilisateur créé"

echo ""
print_success "🎉 RÉINITIALISATION TERMINÉE !"
echo ""
echo "📋 INFORMATIONS DE CONNEXION :"
echo "================================"
echo "Email: <EMAIL>"
echo "Mot de passe: admin123"
echo ""
echo "🚀 Vous pouvez maintenant démarrer l'application :"
echo "./start_gold_sentinel.sh"
