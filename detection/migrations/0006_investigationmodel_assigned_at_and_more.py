# Generated by Django 5.2.1 on 2025-06-04 03:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('detection', '0005_investigationmodel_assignment_notes_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='investigationmodel',
            name='assigned_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='investigationmodel',
            name='assigned_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_by_investigations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='investigationmodel',
            name='priority',
            field=models.CharField(default='MEDIUM', max_length=20),
        ),
    ]
