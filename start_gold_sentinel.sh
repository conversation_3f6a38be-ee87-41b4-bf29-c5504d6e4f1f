#!/bin/bash

# Script de démarrage Gold Sentinel - Frontend + Backend
# Usage: ./start_gold_sentinel.sh

echo "🚀 Démarrage de Gold Sentinel v3.0"
echo "=================================="

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
print_status "Vérification des prérequis..."

# Vérifier Python
if ! command -v python &> /dev/null; then
    print_error "Python n'est pas installé"
    exit 1
fi

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé"
    exit 1
fi

# Vérifier npm
if ! command -v npm &> /dev/null; then
    print_error "npm n'est pas installé"
    exit 1
fi

print_success "Prérequis validés"

# Configuration des variables d'environnement
print_status "Configuration de l'environnement..."

# Créer le fichier .env pour le frontend s'il n'existe pas
if [ ! -f "frontend/.env" ]; then
    print_status "Création du fichier .env pour le frontend..."
    cp frontend/.env.development frontend/.env
    print_success "Fichier .env créé"
fi

# Installation des dépendances backend
print_status "Installation des dépendances Python..."
if [ ! -d "venv" ]; then
    print_status "Création de l'environnement virtuel..."
    python -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt
print_success "Dépendances Python installées"

# Installation des dépendances frontend
print_status "Installation des dépendances Node.js..."
cd frontend
if [ ! -d "node_modules" ]; then
    npm install
    print_success "Dépendances Node.js installées"
else
    print_status "Dépendances Node.js déjà installées"
fi
cd ..

# Migrations de base de données
print_status "Application des migrations de base de données..."
python manage.py migrate
print_success "Migrations appliquées"

# Création du superutilisateur si nécessaire
print_status "Vérification du superutilisateur..."
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(email='<EMAIL>').exists():
    User.objects.create_superuser('<EMAIL>', 'admin123')
    print('Superutilisateur créé: <EMAIL> / admin123')
else:
    print('Superutilisateur existe déjà')
"

# Collecte des fichiers statiques
print_status "Collecte des fichiers statiques..."
python manage.py collectstatic --noinput
print_success "Fichiers statiques collectés"

# Démarrage des serveurs
print_status "Démarrage des serveurs..."

# Fonction pour nettoyer les processus à l'arrêt
cleanup() {
    print_warning "Arrêt des serveurs..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Capturer Ctrl+C pour nettoyer
trap cleanup SIGINT

# Démarrer le backend Django
print_status "Démarrage du backend Django sur http://localhost:8000..."
python manage.py runserver 8000 &
BACKEND_PID=$!

# Attendre que le backend soit prêt
sleep 3

# Vérifier que le backend est démarré
if curl -s http://localhost:8000/api/v1/ > /dev/null; then
    print_success "Backend Django démarré avec succès"
else
    print_error "Échec du démarrage du backend Django"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# Démarrer le frontend React
print_status "Démarrage du frontend React sur http://localhost:5173..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

# Attendre que le frontend soit prêt
sleep 5

print_success "🎉 Gold Sentinel démarré avec succès!"
echo ""
echo "📱 Interface utilisateur: http://localhost:5173"
echo "🔧 API Backend: http://localhost:8000/api/v1/"
echo "👤 Connexion par défaut:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: admin123"
echo ""
echo "📋 Guide de test: frontend/GUIDE_TEST_INTERFACE_COMPLETE.md"
echo ""
print_warning "Appuyez sur Ctrl+C pour arrêter les serveurs"

# Attendre indéfiniment
wait
