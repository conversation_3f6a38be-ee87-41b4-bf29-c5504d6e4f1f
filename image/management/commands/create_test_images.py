from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal

from image.models.image_model import ImageModel
from region.models.region_model import RegionModel


class Command(BaseCommand):
    help = 'Créer des images de test pour le développement'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Nombre d\'images par région (défaut: 5)'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Supprimer les images de test existantes'
        )

    def handle(self, *args, **options):
        count_per_region = options['count']
        clear_existing = options['clear']

        self.stdout.write(
            self.style.SUCCESS('🔍 Création d\'images de test...')
        )

        # Vérifier les régions
        regions = RegionModel.objects.all()
        if not regions.exists():
            self.stdout.write(
                self.style.WARNING('❌ Aucune région trouvée. Création des régions de base...')
            )
            self.create_regions()
            regions = RegionModel.objects.all()

        self.stdout.write(f'✅ {regions.count()} région(s) trouvée(s)')

        # Supprimer les anciennes images de test si demandé
        if clear_existing:
            test_images = ImageModel.objects.filter(name__startswith='Test Image')
            if test_images.exists():
                count = test_images.count()
                test_images.delete()
                self.stdout.write(f'🗑️ {count} ancienne(s) image(s) de test supprimée(s)')

        # Créer les nouvelles images
        images_created = self.create_test_images(regions, count_per_region)

        self.stdout.write(
            self.style.SUCCESS(f'🎉 {images_created} images de test créées!')
        )

        # Afficher les statistiques
        self.show_statistics(regions)

    def create_regions(self):
        """Créer les régions de base"""
        regions_data = [
            {'name': 'BONDOUKOU', 'code': 'BDK'},
            {'name': 'BOUNA', 'code': 'BNA'},
            {'name': 'TANDA', 'code': 'TDA'},
            {'name': 'NASSIAN', 'code': 'NSN'},
        ]

        for region_data in regions_data:
            region, created = RegionModel.objects.get_or_create(
                name=region_data['name'],
                defaults={'code': region_data['code']}
            )
            if created:
                self.stdout.write(f'✅ Région créée: {region.name}')

    def create_test_images(self, regions, count_per_region):
        """Créer les images de test"""
        satellites = ['SENTINEL_2', 'LANDSAT_8', 'SENTINEL_1']
        statuses = ['COMPLETED', 'PROCESSING', 'FAILED']
        
        images_created = 0

        for i, region in enumerate(regions):
            for j in range(count_per_region):
                capture_date = date.today() - timedelta(days=j*7)
                satellite = satellites[j % len(satellites)]
                status = statuses[0] if j < count_per_region-1 else statuses[j % len(statuses)]

                image = ImageModel.objects.create(
                    name=f"Test Image {region.name} #{j+1}",
                    capture_date=capture_date,
                    satellite_source=satellite,
                    cloud_coverage=float(10 + j * 5),
                    resolution=10.0,
                    gee_asset_id=f"test_asset_{region.code.lower()}_{j+1}",
                    gee_collection='COPERNICUS/S2_SR',
                    processing_status=status,
                    processed_at=timezone.now() if status == 'COMPLETED' else None,
                    ndvi_mean=Decimal(str(0.3 + j * 0.1)) if status == 'COMPLETED' else None,
                    ndwi_mean=Decimal(str(0.2 + j * 0.05)) if status == 'COMPLETED' else None,
                    ndti_mean=Decimal(str(0.1 + j * 0.02)) if status == 'COMPLETED' else None,
                    region=region,
                    center_lat=8.0 + i * 0.1,
                    center_lon=-3.0 + i * 0.1,
                )

                images_created += 1
                self.stdout.write(f'✅ {image.name} ({satellite}, {status})')

        return images_created

    def show_statistics(self, regions):
        """Afficher les statistiques"""
        total_images = ImageModel.objects.count()
        self.stdout.write(f'\n📊 Total d\'images en base: {total_images}')

        self.stdout.write('\n📈 Répartition par région:')
        for region in regions:
            count = ImageModel.objects.filter(region=region).count()
            self.stdout.write(f'  - {region.name}: {count} image(s)')

        self.stdout.write('\n📈 Répartition par statut:')
        for status in ['COMPLETED', 'PROCESSING', 'FAILED']:
            count = ImageModel.objects.filter(processing_status=status).count()
            self.stdout.write(f'  - {status}: {count} image(s)')

        # Vérifier la structure pour l'API
        self.stdout.write('\n🔍 Vérification API:')
        recent_images = ImageModel.objects.all()[:3]
        for image in recent_images:
            self.stdout.write(
                f'  - {image.name}: {image.region.name}, {image.processing_status}'
            )
