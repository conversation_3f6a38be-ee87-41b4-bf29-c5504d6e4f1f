#!/usr/bin/env python3
"""
Script de vérification finale pour Gold Sentinel
Teste tous les endpoints critiques et la configuration
"""

import os
import sys
import requests
import json
from urllib.parse import urljoin

# Configuration
BACKEND_URL = "http://localhost:8000"
API_BASE = f"{BACKEND_URL}/api/v1"

def print_status(message):
    print(f"🔍 {message}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def test_backend_health():
    """Test si le backend est accessible"""
    print_status("Test de santé du backend...")
    try:
        response = requests.get(f"{API_BASE}/", timeout=5)
        if response.status_code == 200:
            print_success("Backend accessible")
            return True
        else:
            print_error(f"Backend répond avec status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"Backend inaccessible: {e}")
        return False

def test_cors_configuration():
    """Test de la configuration CORS"""
    print_status("Test de la configuration CORS...")
    try:
        headers = {
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        response = requests.options(f"{API_BASE}/auth/token/", headers=headers, timeout=5)
        
        cors_headers = response.headers
        if 'Access-Control-Allow-Origin' in cors_headers:
            print_success("CORS configuré correctement")
            return True
        else:
            print_error("CORS non configuré pour localhost:5173")
            return False
    except Exception as e:
        print_error(f"Erreur test CORS: {e}")
        return False

def test_authentication_endpoints():
    """Test des endpoints d'authentification"""
    print_status("Test des endpoints d'authentification...")
    
    # Test endpoint login
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        response = requests.post(f"{API_BASE}/auth/token/", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'access' in data and 'refresh' in data:
                print_success("Endpoint de connexion fonctionnel")
                
                # Test endpoint profil avec token
                token = data['access']
                headers = {'Authorization': f'Bearer {token}'}
                profile_response = requests.get(f"{API_BASE}/auth/profile/", headers=headers, timeout=5)
                
                if profile_response.status_code == 200:
                    profile_data = profile_response.json()
                    if 'email' in profile_data and 'authorities' in profile_data:
                        print_success("Endpoint profil fonctionnel")
                        print(f"   Utilisateur: {profile_data.get('email')}")
                        print(f"   Autorités: {profile_data.get('authorities')}")
                        return True
                    else:
                        print_error("Réponse profil incomplète")
                        return False
                else:
                    print_error(f"Endpoint profil échoue: {profile_response.status_code}")
                    return False
            else:
                print_error("Réponse login incomplète (pas de tokens)")
                return False
        else:
            print_error(f"Connexion échoue: {response.status_code}")
            if response.status_code == 401:
                print_warning("Vérifiez que l'utilisateur <EMAIL> existe")
            return False
            
    except Exception as e:
        print_error(f"Erreur test authentification: {e}")
        return False

def test_main_endpoints():
    """Test des endpoints principaux"""
    print_status("Test des endpoints principaux...")
    
    # Connexion pour obtenir un token
    try:
        login_data = {"email": "<EMAIL>", "password": "admin123"}
        auth_response = requests.post(f"{API_BASE}/auth/token/", json=login_data, timeout=10)
        
        if auth_response.status_code != 200:
            print_error("Impossible de se connecter pour tester les endpoints")
            return False
            
        token = auth_response.json()['access']
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test endpoints principaux
        endpoints_to_test = [
            ('/stats/dashboard/', 'Statistiques dashboard'),
            ('/images/', 'Images satellites'),
            ('/detections/', 'Détections'),
            ('/alerts/', 'Alertes'),
            ('/investigations/', 'Investigations'),
            ('/financial-risks/', 'Risques financiers'),
        ]
        
        all_passed = True
        for endpoint, name in endpoints_to_test:
            try:
                response = requests.get(f"{API_BASE}{endpoint}", headers=headers, timeout=5)
                if response.status_code in [200, 404]:  # 404 acceptable si pas de données
                    print_success(f"Endpoint {name} accessible")
                else:
                    print_error(f"Endpoint {name} échoue: {response.status_code}")
                    all_passed = False
            except Exception as e:
                print_error(f"Erreur endpoint {name}: {e}")
                all_passed = False
                
        return all_passed
        
    except Exception as e:
        print_error(f"Erreur test endpoints: {e}")
        return False

def test_analysis_endpoint():
    """Test spécifique de l'endpoint d'analyse"""
    print_status("Test de l'endpoint d'analyse...")
    
    try:
        # Connexion
        login_data = {"email": "<EMAIL>", "password": "admin123"}
        auth_response = requests.post(f"{API_BASE}/auth/token/", json=login_data, timeout=10)
        
        if auth_response.status_code != 200:
            print_error("Impossible de se connecter pour tester l'analyse")
            return False
            
        token = auth_response.json()['access']
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test endpoint analyse (sans lancer vraiment)
        analysis_data = {"months_back": 1}  # Test minimal
        response = requests.post(f"{API_BASE}/analysis/run/", json=analysis_data, headers=headers, timeout=30)
        
        if response.status_code in [200, 201]:
            print_success("Endpoint d'analyse fonctionnel")
            return True
        elif response.status_code == 400:
            print_warning("Endpoint d'analyse accessible mais paramètres rejetés (normal)")
            return True
        else:
            print_error(f"Endpoint d'analyse échoue: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erreur test analyse: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 VÉRIFICATION FINALE - GOLD SENTINEL v3.0")
    print("=" * 50)
    
    tests = [
        ("Santé du backend", test_backend_health),
        ("Configuration CORS", test_cors_configuration),
        ("Endpoints d'authentification", test_authentication_endpoints),
        ("Endpoints principaux", test_main_endpoints),
        ("Endpoint d'analyse", test_analysis_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSÉ" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nRésultat global: {passed}/{total} tests passés")
    
    if passed == total:
        print_success("🎉 TOUS LES TESTS SONT PASSÉS !")
        print("Le système est prêt pour utilisation.")
        return 0
    else:
        print_error("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Vérifiez les erreurs ci-dessus avant de continuer.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
